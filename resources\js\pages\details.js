import { createSlider } from "../appTwo.js";
document.addEventListener("DOMContentLoaded", function () {
    const stripeKey = document.querySelector(
        'meta[name="stripe-key"]'
    )?.content;
    const stripe = Stripe(stripeKey);
    var elements = stripe.elements();

    // Check if mobile
    const isMobile = window.innerWidth < 768;

    // Create card with responsive options
    var cardElement = elements.create("card", {
        style: {
            base: {
                color: "#32325d",
                fontSize: "16px",
                fontWeight: "500",
                fontFamily: "Roboto, Open Sans, Segoe UI, sans-serif",

                fontSmoothing: "antialiased",
                ":-webkit-autofill": {
                    color: "#fce883",
                },
                "::placeholder": {
                    color: "#aab7c4",
                },
                iconColor: "#19345E",
            },
            invalid: {
                color: "#e74c3c",
                iconColor: "#e74c3c",
            },
        },

        hidePostalCode: false,
    });

    // Add mobile class to container if on mobile
    if (isMobile) {
        document
            .querySelector(".stripe-card-element")
            .classList.add("mobile-card-element");
    }

    cardElement.mount("#card-element");

    cardElement.addEventListener("change", function (event) {
        var displayError = document.getElementById("card-errors");
        if (event.error) {
            displayError.textContent = event.error.message;
            displayError.classList.add("show");
        } else {
            displayError.textContent = "";
            displayError.classList.remove("show");
        }
    });

    // Add flags to prevent multiple submissions
    let isProcessing = false;
    let isStripeProcessing = false;

    // Global click blocker function
    const globalClickBlocker = (e) => {
        if (isProcessing) {
            e.preventDefault();
            e.stopPropagation();
            console.log("Global click blocked during payment processing");
            return false;
        }
    };

    // Add direct button click protection
    document
        .getElementById("payment-button")
        .addEventListener("click", function (e) {
            if (isProcessing) {
                e.preventDefault();
                e.stopPropagation();
                console.log(
                    "Button click blocked - payment already in progress"
                );
                return false;
            }
        });

    document
        .getElementById("donationForm")
        .addEventListener("submit", async (e) => {
            e.preventDefault();

            // Immediately set processing flag and disable button to prevent double-clicks
            if (isProcessing) {
                console.log(
                    "Payment already in progress, ignoring duplicate submission"
                );
                return;
            }

            isProcessing = true;
            const submitButton = document.getElementById("payment-button");
            const originalButtonText =
                submitButton.querySelector(".btn-text").textContent;

            // Disable button immediately to prevent double-clicks
            submitButton.disabled = true;
            submitButton.classList.add("processing");
            submitButton.querySelector(".btn-text").textContent =
                "Processing...";
            submitButton.querySelector(".btn-icon i").className =
                "fas fa-spinner fa-spin";

            // Validate card element before showing loader
            const { error: cardError } = await stripe.createPaymentMethod({
                type: "card",
                card: cardElement,
            });

            if (cardError) {
                // Reset button state and show error without loader
                resetButtonState(submitButton, originalButtonText);
                const displayError = document.getElementById("card-errors");
                displayError.textContent = cardError.message;
                displayError.classList.add("show");
                createSlider("error", cardError.message);
                return;
            }

            // Show Full-Screen Loader ONLY after validation passes
            showLoader("Processing Payment...");

            const form = document.getElementById("donationForm");
            const formData = new FormData(form);
            const formObject = {};
            formData.forEach((value, key) => {
                formObject[key] = value;
            });

            try {
                // 1. Create Payment Intent
                updateLoader("Creating secure payment...", 1);
                const response = await fetch(form.action, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify(formObject),
                });
                const { clientSecret } = await response.json();

                if (!clientSecret)
                    throw new Error("Failed to create Payment Intent.");

                // 2. Confirm Card Payment
                updateLoader("Confirming payment with your bank...", 2);

                // Prevent multiple Stripe confirmCardPayment calls
                if (isStripeProcessing) {
                    console.log(
                        "Stripe payment already in progress, aborting duplicate call"
                    );
                    hideLoader();
                    resetButtonState(submitButton, originalButtonText);
                    return;
                }

                isStripeProcessing = true;
                const { paymentIntent, error } =
                    await stripe.confirmCardPayment(clientSecret, {
                        payment_method: {
                            card: cardElement,
                            billing_details: {
                                name: formObject.firstName,
                                email: formObject.email,
                            },
                        },
                    });
                isStripeProcessing = false;

                if (error) {
                    hideLoader();
                    resetButtonState(submitButton, originalButtonText);
                    console.error("Payment Error:", error.message);

                    // Get the current donation token
                    const currentToken =
                        document.getElementById("currentToken").value;

                    // Record the payment failure and mark token as failed
                    await recordPaymentFailure({
                        paymentIntentId:
                            clientSecret.split("_secret_")[0] || "unknown",
                        errorCode: error.code || "unknown",
                        errorMessage: error.message,
                        errorType: error.type || "card_error",
                        amount: formObject.amount,
                        customerEmail: formObject.email,
                        customerName:
                            formObject.firstName +
                            " " +
                            (formObject.lastName || ""),
                        donationType: "user_donation",
                        donatedToSlug: formObject.donatedTo,
                        donationMessage: formObject.message,
                        paymentMethodType: "card",
                        failureStage: "client_side_payment_confirmation",
                    });

                    // Note: Donation token will be marked as failed via Stripe webhook
                    // Client-side marking is kept as backup but webhook is primary
                    await markDonationTokenAsFailed(
                        currentToken,
                        error.message
                    );

                    createSlider("error", error.message);
                } else if (paymentIntent.status === "succeeded") {
                    console.log("Payment successful!");

                    // 3. Send final confirmation to backend
                    updateLoader("Completing your donation...", 3);
                    formObject.paymentIntentId = paymentIntent.id;

                    formObject.donationToken = document
                        .getElementById("currentToken")
                        .value;

                    fetch("/store-transaction", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": document
                                .querySelector('meta[name="csrf-token"]')
                                .getAttribute("content"),
                        },
                        body: JSON.stringify(formObject),
                    })
                        .then((res) => res.json())
                        .then(async (data) => {
                            if (data.success) {
                                // Note: Donation token will be marked as successful via Stripe webhook
                                // Client-side marking is kept as backup but webhook is primary
                                const currentToken =
                                    document.getElementById(
                                        "currentToken"
                                    ).value;
                                await markDonationTokenAsSuccess(
                                    currentToken,
                                    paymentIntent.id
                                );

                                // Don't reset button state on success - user will be redirected
                                showSuccessAnimation(() => {
                                    window.location.href = data.redirect_url;
                                });
                            } else {
                                hideLoader();
                                resetButtonState(
                                    submitButton,
                                    originalButtonText
                                );
                                createSlider("error", data.message);
                            }
                        })
                        .catch((err) => {
                            hideLoader();
                            resetButtonState(submitButton, originalButtonText);
                            createSlider("error", err);
                        });
                }
            } catch (err) {
                hideLoader();
                resetButtonState(submitButton, originalButtonText);
                console.error("Unexpected Error:", err);
            }
        });

    // Show Full-Screen Loader with enhanced styling
    function showLoader(message) {
        if (!document.getElementById("full-screen-loader")) {
            const loader = document.createElement("div");
            loader.id = "full-screen-loader";
            loader.innerHTML = `
        <div class="loader-content">
            <div class="loader-icon">
                <div class="spinner-border" role="status"></div>
            </div>
            <p class="loader-message">${message}</p>
            <div class="loader-steps">
                <div class="step active">
                    <div class="step-icon">1</div>
                    <span>Processing Payment</span>
                </div>
                <div class="step">
                    <div class="step-icon">2</div>
                    <span>Confirming Transaction</span>
                </div>
                <div class="step">
                    <div class="step-icon">3</div>
                    <span>Completing Donation</span>
                </div>
            </div>
        </div>
    `;
            // Prevent any clicks or interactions while processing
            loader.addEventListener("click", (e) => {
                e.preventDefault();
                e.stopPropagation();
            });

            loader.addEventListener("keydown", (e) => {
                e.preventDefault();
                e.stopPropagation();
            });

            // Add global event blocker
            document.addEventListener("click", globalClickBlocker, true);
            loader.setAttribute("data-global-blocker", "true");

            document.body.appendChild(loader);
        }
    }

    // Update loader message and progress
    function updateLoader(message, step = 1) {
        const loader = document.getElementById("full-screen-loader");
        if (loader) {
            const messageEl = loader.querySelector(".loader-message");
            const steps = loader.querySelectorAll(".step");

            if (messageEl) messageEl.textContent = message;

            // Update step indicators
            steps.forEach((stepEl, index) => {
                if (index < step) {
                    stepEl.classList.add("active");
                    stepEl.classList.add("completed");
                } else if (index === step - 1) {
                    stepEl.classList.add("active");
                    stepEl.classList.remove("completed");
                } else {
                    stepEl.classList.remove("active", "completed");
                }
            });
        }
    }

    // Hide Loader
    function hideLoader() {
        const loader = document.getElementById("full-screen-loader");
        if (loader) {
            // Remove global event blocker if it was added
            if (loader.getAttribute("data-global-blocker") === "true") {
                document.removeEventListener("click", globalClickBlocker, true);
            }
            loader.remove();
        }
    }

    function showSuccessAnimation(callback) {
        const loader = document.getElementById("full-screen-loader");
        if (loader) {
            loader.innerHTML = `
        <div class="success-animation">
            🎉
        </div>
        <p style="color:white;">Thank You for Your Donation!</p>
    `;
            setTimeout(() => {
                callback();
            }, 2000);
        }
    }

    // Function to reset button state
    function resetButtonState(button, originalText) {
        isProcessing = false;
        isStripeProcessing = false;
        button.disabled = false;
        button.classList.remove("processing");
        button.querySelector(".btn-text").textContent = originalText;
        button.querySelector(".btn-icon i").className = "fas fa-heart";
    }

    // Function to record payment failures
    async function recordPaymentFailure(failureData) {
        try {
            const response = await fetch("/record-payment-failure", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
                body: JSON.stringify(failureData),
            });

            const result = await response.json();

            if (result.success) {
                console.log("Payment failure recorded successfully");
            } else {
                console.error(
                    "Failed to record payment failure:",
                    result.message
                );
            }
        } catch (error) {
            console.error("Error recording payment failure:", error);
        }
    }

    // Function to mark donation token as failed
    async function markDonationTokenAsFailed(token, failureReason) {
        try {
            const response = await fetch("/donation-token/failure", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
                body: JSON.stringify({
                    token: token,
                    failureReason: failureReason,
                    regenerateToken: true, // Automatically regenerate token for retry
                }),
            });

            const result = await response.json();

            if (result.success) {
                console.log("Donation token marked as failed");
                if (result.newToken) {
                    // Update the current token to the new one for retry
                    document.getElementById("currentToken").value =
                        result.newToken;
                    console.log(
                        "New token generated for retry:",
                        result.newToken
                    );
                }
            } else {
                console.error(
                    "Failed to mark donation token as failed:",
                    result.message
                );
            }
        } catch (error) {
            console.error("Error marking donation token as failed:", error);
        }
    }

    // Function to mark donation token as successful
    async function markDonationTokenAsSuccess(token, paymentIntentId) {
        try {
            const response = await fetch("/donation-token/success", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
                body: JSON.stringify({
                    token: token,
                    paymentIntentId: paymentIntentId,
                }),
            });

            const result = await response.json();

            if (result.success) {
                console.log("Donation token marked as successful");
            } else {
                console.error(
                    "Failed to mark donation token as successful:",
                    result.message
                );
            }
        } catch (error) {
            console.error("Error marking donation token as successful:", error);
        }
    }
});
