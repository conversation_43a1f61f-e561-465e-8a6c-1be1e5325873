<header class="header py-3">
    <div class="container d-flex align-items-center">
        <div class="brand">
            <a href="/"><img src="{{ asset('images/logo.png') }}" alt="" height="126" width="165" /></a>
        </div>
        <div class="call_nav align-items-center d-flex flex-column d-lg-none justify-content-center ms-auto"
            id="navCall"><span class="line"></span><span class="line"></span><span class="line"></span></div>
        <nav class="site_nav ms-lg-auto d-none d-lg-flex align-items-center" id="siteNav">
            <ul class="nav text-uppercase">
                <li class="{{ request()->routeIs('home') ? 'active' : '' }}">
                    <a href="{{ route('home') }}">Gauntlet Home</a>
                </li>
                <li class="{{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                    <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                </li>
                <li class="{{ request()->routeIs('admin.donationList') ? 'active' : '' }}">
                    <a href="{{ route('admin.donationList') }}">Donations</a>
                </li>
                <li class="{{ request()->routeIs('admin.messages.index') ? 'active' : '' }}">
                    <a href="{{ route('admin.messages.index') }}">Messages</a>
                </li>
                <li class="{{ request()->routeIs('admin.faqs') ? 'active' : '' }}">
                    <a href="{{ route('admin.faqs') }}">Faq's</a>
                </li>
                <li
                    class="{{ request()->routeIs('admin.mailView') || request()->routeIs('admin.send-mail') ? 'active' : '' }}">
                    <a href="{{ route('admin.send-mail') }}">Send Mail</a>
                </li>

                <li>
                    <a href="{{ route('logout') }}"
                        onclick="event.preventDefault(); document.getElementById('logout-form-admin').submit();">
                        Logout</a>
                    <form id="logout-form-admin" action="{{ route('logout') }}" method="POST" class="d-none">
                        @csrf
                    </form>
                </li>
            </ul>
        </nav>
    </div>
</header>

<div id="loader">
    <div class="loader">
        <div class="ball">
            <div class="floor"></div>
        </div>
        <div class="ball">
            <div class="floor"></div>
        </div>
        <div class="ball">
            <div class="floor"></div>
        </div>
    </div>
</div>



{{-- <!-- admin.nav.bar.blade.php -->
<div class="admin-sidebar bg-white shadow-lg" id="adminSidebar">
    <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
        <h5 class="m-0 fw-bold sidebar-title" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
            <span class="title-full">Admin Panel</span>
            <span class="title-short">AP</span>
        </h5>
        <button class="btn toggle-sidebar" id="closeSidebar">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="p-2">
        <div class="admin-profile text-center py-3">
            <h6 class="mb-1 admin-name">ADMIN</h6>
            <span class="badge rounded-pill" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);">
                {{Auth::user()->name}}
            </span>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a href="{{route('admin.dashboard')}}" class="nav-link active">
                    <i class="fas fa-tachometer-alt me-2"></i> <span class="menu-text">Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{route('admin.donationList')}}" class="nav-link">
                    <i class="fas fa-hand-holding-usd me-2"></i> <span class="menu-text">Donations</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="" class="nav-link">
                    <i class="fas fa-chart-bar me-2"></i> <span class="menu-text">Reports</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{route('admin.faqs')}}" class="nav-link">
                    <i class="fas fa-question-circle me-2"></i>
                    <span class="menu-text">FAQ's</span>
                </a>
            </li>


            <li class="nav-item">
                <a href="{{route('admin.mailView')}}" class="nav-link">
                    <i class="fas fa-paper-plane me-2"></i> <span class="menu-text">Send Mail</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{route('admin.messages.index')}}" class="nav-link">
                    <i class="fa-solid fa-message"></i> <span class="menu-text">Messages</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{route('admin.failed-transactions.index')}}" class="nav-link">
                    <i class="fas fa-exclamation-triangle me-2"></i> <span class="menu-text">Failed Transactions</span>
                </a>
            </li>

            <li class="nav-item mt-3">
                <a href="{{ route('logout') }}" class="nav-link text-danger"
                   onclick="event.preventDefault(); document.getElementById('logout-form-admin').submit();">
                    <i class="fas fa-sign-out-alt me-2"></i> <span class="menu-text">Logout</span>
                </a>
                <form id="logout-form-admin" action="{{ route('logout') }}" method="POST" class="d-none">
                    @csrf
                </form>
            </li>
        </ul>
    </div>
</div>


<div class="sidebar-overlay" id="sidebarOverlay"></div>


<button class="btn position-fixed toggle-sidebar-open" id="toggleSidebar">
    <i class="fas fa-bars"></i>
</button>


<style>

    .admin-sidebar {
        width: 280px;
        height: 100vh;
        position: fixed;
        left: -280px;
        top: 0;
        transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 1040;
        overflow-y: auto;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-right: 1px solid rgba(0,0,0,0.08);
    }

    .admin-sidebar.active {
        transform: translateX(280px);
    }


    .title-short {
        display: none;
    }


    .toggle-sidebar-open {
        left: 15px;
        top: 15px;
        z-index: 1030;
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        color: white;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        border: none;
        transition: all 0.3s ease;
        position: fixed;
    }

    .toggle-sidebar-open:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 15px rgba(0,0,0,0.25);
    }


    .toggle-sidebar {
        background: transparent;
        border: none;
        color: #6a11cb;
        transition: transform 0.3s ease;
    }

    .toggle-sidebar:hover {
        transform: rotate(90deg);
    }


    .nav-item {
        margin-bottom: 5px;
    }

    .nav-link {
        border-radius: 8px;
        transition: all 0.2s ease;
        padding: 10px 15px;
        color: #495057;
    }

    .nav-link:hover {
        background-color: rgba(106, 17, 203, 0.1);
        transform: translateX(5px);
    }

    .nav-link.active {
        background: linear-gradient(135deg, rgba(106, 17, 203, 0.15) 0%, rgba(37, 117, 252, 0.15) 100%);
        color: #6a11cb;
        font-weight: 500;
    }


    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1035;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }


    .admin-content {
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
        padding-left: 60px;
    }

    .fixed-top, .sticky-top {
        padding-left: 60px !important;
    }
</style>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.getElementById('adminSidebar');
        const toggleBtn = document.getElementById('toggleSidebar');
        const closeBtn = document.getElementById('closeSidebar');
        const overlay = document.getElementById('sidebarOverlay');


        localStorage.setItem('sidebarOpen', 'false');


        function openSidebar() {
            sidebar.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }


        function closeSidebar() {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }


        toggleBtn.addEventListener('click', function() {
            openSidebar();
        });

        closeBtn.addEventListener('click', function() {
            closeSidebar();
        });


        overlay.addEventListener('click', function() {
            closeSidebar();
        });


        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
            }
        });


        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    closeSidebar();
                }
            });
        });
    });
</script> --}}
