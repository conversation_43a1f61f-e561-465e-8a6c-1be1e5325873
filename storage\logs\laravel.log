[2025-08-01 08:22:13] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 08:23:48] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 08:23:49] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:mode...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 08:24:41] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'asfl_gauntlet.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1754036681) or (`reserved_at` <= 1754036591)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'asfl_gauntlet.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1754036681) or (`reserved_at` <= 1754036591)) order by `id` asc limit 1 for update) at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MySqlConnection))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'asfl_gauntlet.jobs' doesn't exist at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MySqlConnection))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-08-01 08:25:44] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-user...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewUserRegisteredNotification))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '9d451ed5-fd1a-4...', Object(App\\Notifications\\NewUserRegisteredNotification), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewUserRegisteredNotification), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewUserRegisteredNotification), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 08:26:34] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrEISKQoAc42Xce1pZjj5pY","idempotency_key":"payment_intent_b433077ae7e47d5abd0744357f8bd8d3","email":"<EMAIL>","amount":"250.00"} 
[2025-08-01 08:26:34] local.INFO: Stripe webhook route hit  
[2025-08-01 08:26:34] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrEISKQoAc42Xce1rNl4zvY","event_type":"payment_intent.created"} 
[2025-08-01 08:26:34] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 08:26:36] local.INFO: Stripe webhook route hit  
[2025-08-01 08:26:36] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrEISKQoAc42Xce1fZ8KMSm","event_type":"payment_intent.succeeded"} 
[2025-08-01 08:26:36] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrEISKQoAc42Xce1pZjj5pY","amount":25000,"customer_id":null} 
[2025-08-01 08:26:36] local.INFO: Donation token marked as successful via webhook {"token":"iGjL7aNiRUi6KPwEiwwcc6EatFpzjZXX","payment_intent_id":"pi_3RrEISKQoAc42Xce1pZjj5pY","amount":250} 
[2025-08-01 08:26:38] local.INFO: Stripe webhook route hit  
[2025-08-01 08:26:38] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrEISKQoAc42Xce187cvPDb","event_type":"charge.succeeded"} 
[2025-08-01 08:26:38] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrEISKQoAc42Xce1VMwFgWI","object":"charge","amount":25000,"amount_captured":25000,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12344","state":null},"email":"<EMAIL>","name":"Vladimir","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754036798,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"a message of hope","anonymous_to_all":"false","first_name":"Vladimir","donation_token":"iGjL7aNiRUi6KPwEiwwcc6EatFpzjZXX","paymentFrequency":"one-time","anonymous_to_public":"false","idempotency_key":"payment_intent_b433077ae7e47d5abd0744357f8bd8d3","address":"fadfad","state":"Eveniet exercitatio","email":"<EMAIL>","city":"ff","last_name":"Clemons"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":63,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrEISKQoAc42Xce1pZjj5pY","payment_method":"pm_1RrEITKQoAc42Xce71juLt9i","payment_method_details":{"card":{"amount_authorized":25000,"authorization_code":"415255","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":4,"exp_year":2039,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":25000,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKL70scQGMgbcKerLWY06LBaUT1xpNNfPooNwLc3qePdhmx_js1WL7VyYwBasKiRIzoAQkJZTpy9VnPAX","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 08:26:38] local.INFO: metadata {"metadata":{"donated_to":"ezekiel-hardy","message":"a message of hope","anonymous_to_all":"false","first_name":"Vladimir","donation_token":"iGjL7aNiRUi6KPwEiwwcc6EatFpzjZXX","paymentFrequency":"one-time","anonymous_to_public":"false","idempotency_key":"payment_intent_b433077ae7e47d5abd0744357f8bd8d3","address":"fadfad","state":"Eveniet exercitatio","email":"<EMAIL>","city":"ff","last_name":"Clemons"}} 
[2025-08-01 08:26:38] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrEISKQoAc42Xce1VMwFgWI","amount":250,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKL70scQGMgbcKerLWY06LBaUT1xpNNfPooNwLc3qePdhmx_js1WL7VyYwBasKiRIzoAQkJZTpy9VnPAX","receipt_number":null} 
[2025-08-01 08:26:39] local.INFO: Stripe webhook route hit  
[2025-08-01 08:26:39] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrEISKQoAc42Xce1hpaiqPd","event_type":"charge.updated"} 
[2025-08-01 08:26:39] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 08:26:44] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), 'fc03a7c7-757b-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 08:26:49] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), 'ebb65f23-2cf1-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 08:28:33] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 08:29:20] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrEL8KQoAc42Xce1CHbJNfR","idempotency_key":"payment_intent_6d1ac959a828f815e9346b67889feace","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 08:29:20] local.INFO: Stripe webhook route hit  
[2025-08-01 08:29:20] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrEL8KQoAc42Xce1J09nQTq","event_type":"payment_intent.created"} 
[2025-08-01 08:29:20] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 08:29:22] local.INFO: Stripe webhook route hit  
[2025-08-01 08:29:22] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrEL8KQoAc42Xce1TeiEjVb","event_type":"payment_intent.succeeded"} 
[2025-08-01 08:29:22] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrEL8KQoAc42Xce1CHbJNfR","amount":2500,"customer_id":null} 
[2025-08-01 08:29:22] local.INFO: Donation token marked as successful via webhook {"token":"VXQE1Y1xTmD6j2VUTL4nCCzjL38QtBTr","payment_intent_id":"pi_3RrEL8KQoAc42Xce1CHbJNfR","amount":25} 
[2025-08-01 08:29:22] local.INFO: Stripe webhook route hit  
[2025-08-01 08:29:22] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrEL8KQoAc42Xce1W1IEQST","event_type":"charge.succeeded"} 
[2025-08-01 08:29:22] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrEL8KQoAc42Xce10y6ORbX","object":"charge","amount":2500,"amount_captured":2500,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Urielle","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754036963,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Id necessitatibus ul","anonymous_to_all":"false","first_name":"Urielle","paymentFrequency":"one-time","donation_token":"VXQE1Y1xTmD6j2VUTL4nCCzjL38QtBTr","anonymous_to_public":"true","idempotency_key":"payment_intent_6d1ac959a828f815e9346b67889feace","address":"Ipsum consectetur v","state":"Sed non aliquid corr","email":"<EMAIL>","city":"Non labore nisi moll","last_name":"Flores"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":0,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrEL8KQoAc42Xce1CHbJNfR","payment_method":"pm_1RrEL9KQoAc42XceYzeXs6TU","payment_method_details":{"card":{"amount_authorized":2500,"authorization_code":"400131","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2040,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":2500,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKOT1scQGMgYoqCDg0246LBYAqXjSdXxmrooZOGEIpf4va0ERI6Gw1DCzPx0UTSUlNGkGSggnoKs3IWh2","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 08:29:22] local.WARNING: Charge succeeded but no donor found {"charge_id":"ch_3RrEL8KQoAc42Xce10y6ORbX","customer_id":null,"amount":2500} 
[2025-08-01 08:29:24] local.INFO: Stripe webhook route hit  
[2025-08-01 08:29:24] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrEL8KQoAc42Xce1qLpRWm2","event_type":"charge.updated"} 
[2025-08-01 08:29:24] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 08:30:09] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 08:30:24] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 08:30:29] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrEMGKQoAc42Xce120tH5AO","idempotency_key":"payment_intent_34b3b1ef1482ae5e789ba4ae78ed6010","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 08:30:29] local.INFO: Stripe webhook route hit  
[2025-08-01 08:30:30] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrEMGKQoAc42Xce1yPFeVfo","event_type":"payment_intent.created"} 
[2025-08-01 08:30:30] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 08:30:32] local.INFO: Stripe webhook route hit  
[2025-08-01 08:30:32] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrEMGKQoAc42Xce12R0zpzH","event_type":"payment_intent.succeeded"} 
[2025-08-01 08:30:32] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrEMGKQoAc42Xce120tH5AO","amount":2500,"customer_id":null} 
[2025-08-01 08:30:32] local.INFO: Stripe webhook route hit  
[2025-08-01 08:30:32] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrEMGKQoAc42Xce12dnYbMd","event_type":"charge.succeeded"} 
[2025-08-01 08:30:32] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrEMGKQoAc42Xce1ultoBUA","object":"charge","amount":2500,"amount_captured":2500,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Urielle","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754037033,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Id necessitatibus ul","anonymous_to_all":"false","first_name":"Urielle","idempotency_key":"payment_intent_34b3b1ef1482ae5e789ba4ae78ed6010","donation_token":"VXQE1Y1xTmD6j2VUTL4nCCzjL38QtBTr","anonymous_to_public":"true","paymentFrequency":"one-time","address":"Ipsum consectetur v","state":"Sed non aliquid corr","email":"<EMAIL>","city":"Non labore nisi moll","last_name":"Flores"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":50,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrEMGKQoAc42Xce120tH5AO","payment_method":"pm_1RrEMGKQoAc42XceVQ4jHGC7","payment_method_details":{"card":{"amount_authorized":2500,"authorization_code":"106239","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2040,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":2500,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKKn2scQGMgbWv6f8OWg6LBbl8mtXFSlPXS0Tnq9PobtDvpijvp4MAlNr1YTPtKXlqiqX-Yml4uftcnOQ","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 08:30:32] local.INFO: metadata {"metadata":{"donated_to":"ezekiel-hardy","message":"Id necessitatibus ul","anonymous_to_all":"false","first_name":"Urielle","idempotency_key":"payment_intent_34b3b1ef1482ae5e789ba4ae78ed6010","donation_token":"VXQE1Y1xTmD6j2VUTL4nCCzjL38QtBTr","anonymous_to_public":"true","paymentFrequency":"one-time","address":"Ipsum consectetur v","state":"Sed non aliquid corr","email":"<EMAIL>","city":"Non labore nisi moll","last_name":"Flores"}} 
[2025-08-01 08:30:32] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrEMGKQoAc42Xce1ultoBUA","amount":25,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKKn2scQGMgbWv6f8OWg6LBbl8mtXFSlPXS0Tnq9PobtDvpijvp4MAlNr1YTPtKXlqiqX-Yml4uftcnOQ","receipt_number":null} 
[2025-08-01 08:30:33] local.INFO: Stripe webhook route hit  
[2025-08-01 08:30:33] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrEMGKQoAc42Xce1zl9IIOo","event_type":"charge.updated"} 
[2025-08-01 08:30:33] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 08:30:38] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '5d2137b4-6b85-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 08:30:43] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), '935956ec-f884-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 09:43:34] local.ERROR: The process "C:\xampp\php\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1" exceeded the timeout of 60 seconds. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\ProcessTimedOutException(code: 0): The process \"C:\\xampp\\php\\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1\" exceeded the timeout of 60 seconds. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\process\\Process.php:1181)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\process\\Process.php(450): Symfony\\Component\\Process\\Process->checkTimeout()
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\process\\Process.php(251): Symfony\\Component\\Process\\Process->wait()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(180): Symfony\\Component\\Process\\Process->run(Object(Closure))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(91): Illuminate\\Queue\\Listener->runProcess(Object(Symfony\\Component\\Process\\Process), '128')
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListenCommand.php(74): Illuminate\\Queue\\Listener->listen(NULL, 'default', Object(Illuminate\\Queue\\ListenerOptions))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListenCommand->handle()
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListenCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 
[2025-08-01 09:51:22] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:51:44] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:57:39] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:58:47] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:59:15] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:59:16] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:mode...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:59:20] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:59:21] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:mode...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-01 09:59:58] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrFkrKQoAc42Xce0BamDKvu","idempotency_key":"payment_intent_c874e474f621175df1c8da939c23ea36","email":"<EMAIL>","amount":"500.00"} 
[2025-08-01 09:59:58] local.INFO: Stripe webhook route hit  
[2025-08-01 09:59:58] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrFkrKQoAc42Xce0l3LOYN4","event_type":"payment_intent.created"} 
[2025-08-01 09:59:58] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 10:00:01] local.INFO: Stripe webhook route hit  
[2025-08-01 10:00:01] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrFkrKQoAc42Xce0N8IzeQJ","event_type":"payment_intent.succeeded"} 
[2025-08-01 10:00:01] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrFkrKQoAc42Xce0BamDKvu","amount":50000,"customer_id":null} 
[2025-08-01 10:00:01] local.INFO: Donation token marked as successful via webhook {"token":"9JBEIYF8GwyAc9Qo1edlh7lzRqcb1njY","payment_intent_id":"pi_3RrFkrKQoAc42Xce0BamDKvu","amount":500} 
[2025-08-01 10:00:01] local.INFO: Stripe webhook route hit  
[2025-08-01 10:00:01] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrFkrKQoAc42Xce0UMm5jWA","event_type":"charge.succeeded"} 
[2025-08-01 10:00:01] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrFkrKQoAc42Xce0IPYVizY","object":"charge","amount":50000,"amount_captured":50000,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Brenda","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754042403,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Distinctio Eu iusto","anonymous_to_all":"true","first_name":"Brenda","idempotency_key":"payment_intent_c874e474f621175df1c8da939c23ea36","paymentFrequency":"one-time","anonymous_to_public":"true","donation_token":"9JBEIYF8GwyAc9Qo1edlh7lzRqcb1njY","address":"Velit dolorum consec","email":"<EMAIL>","state":"Dolor id aliquid ad","city":"Eum perspiciatis qu","last_name":"Caldwell"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":54,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrFkrKQoAc42Xce0BamDKvu","payment_method":"pm_1RrFksKQoAc42Xce0JOVj2ui","payment_method_details":{"card":{"amount_authorized":50000,"authorization_code":"242232","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2030,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":50000,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKKOgssQGMgbRtrHBvfY6LBajl-r9XBXeuvDXTK8M70ZJfzzNSPyPL1uyKhWPduBuF2wE2ji2GxBXqLgS","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 10:00:01] local.INFO: metadata {"metadata":{"donated_to":"ezekiel-hardy","message":"Distinctio Eu iusto","anonymous_to_all":"true","first_name":"Brenda","idempotency_key":"payment_intent_c874e474f621175df1c8da939c23ea36","paymentFrequency":"one-time","anonymous_to_public":"true","donation_token":"9JBEIYF8GwyAc9Qo1edlh7lzRqcb1njY","address":"Velit dolorum consec","email":"<EMAIL>","state":"Dolor id aliquid ad","city":"Eum perspiciatis qu","last_name":"Caldwell"}} 
[2025-08-01 10:00:01] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrFkrKQoAc42Xce0IPYVizY","amount":500,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKKOgssQGMgbRtrHBvfY6LBajl-r9XBXeuvDXTK8M70ZJfzzNSPyPL1uyKhWPduBuF2wE2ji2GxBXqLgS","receipt_number":null} 
[2025-08-01 10:00:02] local.INFO: Stripe webhook route hit  
[2025-08-01 10:00:02] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrFkrKQoAc42Xce0moHnQuR","event_type":"charge.updated"} 
[2025-08-01 10:00:02] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 10:00:05] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), 'a64af28a-173d-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 10:00:10] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), 'b4106b5b-5cb6-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 10:02:19] local.INFO: Stripe webhook route hit  
[2025-08-01 10:02:19] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrFn8KQoAc42Xce1HWRxkbk","event_type":"payment_intent.created"} 
[2025-08-01 10:02:19] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 10:02:20] local.INFO: Stripe webhook route hit  
[2025-08-01 10:02:20] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrFn8KQoAc42Xce19ZuY0ps","event_type":"payment_intent.succeeded"} 
[2025-08-01 10:02:20] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrFn8KQoAc42Xce1GWxAHG4","amount":2500,"customer_id":null} 
[2025-08-01 10:02:21] local.INFO: Stripe webhook route hit  
[2025-08-01 10:02:21] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrFn8KQoAc42Xce1fU0LbFx","event_type":"charge.succeeded"} 
[2025-08-01 10:02:21] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrFn8KQoAc42Xce1HZEBHtz","object":"charge","amount":2500,"amount_captured":2500,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Athena Everett","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754042543,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":49,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrFn8KQoAc42Xce1GWxAHG4","payment_method":"pm_1RrFn8KQoAc42XceU2NRPsTR","payment_method_details":{"card":{"amount_authorized":2500,"authorization_code":"517419","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2030,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":2500,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKK-hssQGMgZEtTuafkQ6LBYQbNiM6yuON5k1rZyXIMI9_8vez0tGkEl_iZWzNtzEDBsumpmMebshX9xU","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 10:02:21] local.INFO: metadata {"metadata":[]} 
[2025-08-01 10:02:21] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrFn8KQoAc42Xce1HZEBHtz","amount":25,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKK-hssQGMgZEtTuafkQ6LBYQbNiM6yuON5k1rZyXIMI9_8vez0tGkEl_iZWzNtzEDBsumpmMebshX9xU","receipt_number":null} 
[2025-08-01 10:02:22] local.INFO: Stripe webhook route hit  
[2025-08-01 10:02:23] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrFn8KQoAc42Xce1zEGZFbk","event_type":"charge.updated"} 
[2025-08-01 10:02:23] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 10:02:29] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), 'b045eec4-645d-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 10:02:35] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), '4bedf3e0-5221-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 10:20:31] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrG4lKQoAc42Xce1XKgGSB5","idempotency_key":"payment_intent_97541631664ea2e01e882d219341317e","email":"<EMAIL>","amount":"500.00"} 
[2025-08-01 10:20:32] local.INFO: Stripe webhook route hit  
[2025-08-01 10:20:32] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrG4lKQoAc42Xce12NZ0nLK","event_type":"payment_intent.created"} 
[2025-08-01 10:20:32] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 10:20:33] local.INFO: Stripe webhook route hit  
[2025-08-01 10:20:33] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrG4lKQoAc42Xce1ymrcqf4","event_type":"payment_intent.succeeded"} 
[2025-08-01 10:20:33] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrG4lKQoAc42Xce1XKgGSB5","amount":50000,"customer_id":null} 
[2025-08-01 10:20:33] local.INFO: Donation token marked as successful via webhook {"token":"nh8R6eeGdwVJQxElqEm7O6jw42wr0C9q","payment_intent_id":"pi_3RrG4lKQoAc42Xce1XKgGSB5","amount":500} 
[2025-08-01 10:20:35] local.INFO: Stripe webhook route hit  
[2025-08-01 10:20:35] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrG4lKQoAc42Xce1rLL0P6w","event_type":"charge.succeeded"} 
[2025-08-01 10:20:35] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrG4lKQoAc42Xce1shRlem2","object":"charge","amount":50000,"amount_captured":50000,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Kimberly","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754043636,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Esse veritatis sint","anonymous_to_all":"false","first_name":"Kimberly","idempotency_key":"payment_intent_97541631664ea2e01e882d219341317e","paymentFrequency":"one-time","anonymous_to_public":"true","donation_token":"nh8R6eeGdwVJQxElqEm7O6jw42wr0C9q","address":"Quis eos nobis tenet","state":"Sed harum ut in culp","email":"<EMAIL>","city":"Corrupti et rerum o","last_name":"Koch"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":51,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrG4lKQoAc42Xce1XKgGSB5","payment_method":"pm_1RrG4mKQoAc42XcetnbmZ4CU","payment_method_details":{"card":{"amount_authorized":50000,"authorization_code":"030251","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2030,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":50000,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKPWpssQGMgYGm3juJ6U6LBYQVms8ckSGS_p--rdPJ5FVpIDZrIRPHBXbcNQhJwDahj3KPtpZ5xbdME99","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 10:20:35] local.INFO: metadata {"metadata":{"donated_to":"ezekiel-hardy","message":"Esse veritatis sint","anonymous_to_all":"false","first_name":"Kimberly","idempotency_key":"payment_intent_97541631664ea2e01e882d219341317e","paymentFrequency":"one-time","anonymous_to_public":"true","donation_token":"nh8R6eeGdwVJQxElqEm7O6jw42wr0C9q","address":"Quis eos nobis tenet","state":"Sed harum ut in culp","email":"<EMAIL>","city":"Corrupti et rerum o","last_name":"Koch"}} 
[2025-08-01 10:20:35] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrG4lKQoAc42Xce1shRlem2","amount":500,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKPWpssQGMgYGm3juJ6U6LBYQVms8ckSGS_p--rdPJ5FVpIDZrIRPHBXbcNQhJwDahj3KPtpZ5xbdME99","receipt_number":null} 
[2025-08-01 10:20:35] local.INFO: Stripe webhook route hit  
[2025-08-01 10:20:35] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrG4lKQoAc42Xce147NoDRg","event_type":"charge.updated"} 
[2025-08-01 10:20:35] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 10:20:42] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '5287ce12-8f89-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 10:20:48] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), '34a1ffc1-f670-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 10:21:52] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrG63KQoAc42Xce1wOmhsjP","idempotency_key":"payment_intent_9b705c8af8df07bc9a99ab03bfe338cf","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:21:52] local.INFO: Stripe webhook route hit  
[2025-08-01 10:21:52] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrG63KQoAc42Xce1F1z6BCU","event_type":"payment_intent.created"} 
[2025-08-01 10:21:52] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 10:21:53] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrG63KQoAc42Xce1wOmhsjP","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:21:53] local.INFO: Stripe webhook route hit  
[2025-08-01 10:21:54] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrG63KQoAc42Xce1VJESmn5","event_type":"charge.failed"} 
[2025-08-01 10:21:54] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:21:54] local.INFO: Stripe webhook route hit  
[2025-08-01 10:21:54] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrG63KQoAc42Xce1HXuLlVf","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:21:54] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrG63KQoAc42Xce1wOmhsjP","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:21:54] local.INFO: Donation token marked as failed via webhook {"token":"7eerFLPBBD8eKHZzSJqngsM3YjH9sEHz","payment_intent_id":"pi_3RrG63KQoAc42Xce1wOmhsjP","failure_reason":"Your card has insufficient funds."} 
[2025-08-01 10:22:37] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_9b705c8af8df07bc9a99ab03bfe338cf","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:22:39] local.INFO: Stripe webhook route hit  
[2025-08-01 10:22:39] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrG63KQoAc42Xce1QXHxGcZ","event_type":"charge.failed"} 
[2025-08-01 10:22:39] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:22:40] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrG63KQoAc42Xce1wOmhsjP","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:22:40] local.INFO: Stripe webhook route hit  
[2025-08-01 10:22:40] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrG63KQoAc42Xce1DFS6Nuj","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:22:40] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrG63KQoAc42Xce1wOmhsjP","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:02] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:02] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:02] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrGA5KQoAc42Xce1RL9ujwO","event_type":"payment_intent.created"} 
[2025-08-01 10:26:02] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 10:26:03] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:03] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:03] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1zcoFb8S","event_type":"charge.failed"} 
[2025-08-01 10:26:03] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:04] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:04] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce1POEe8wo","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:04] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:05] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:07] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:07] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1sytSWZQ","event_type":"charge.failed"} 
[2025-08-01 10:26:07] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:07] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:07] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:07] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce140MtwSx","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:07] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:09] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:10] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:11] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:11] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1cta5Unb","event_type":"charge.failed"} 
[2025-08-01 10:26:11] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:11] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:11] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce1o4pgnPw","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:11] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:12] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:14] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:14] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1dpTube3","event_type":"charge.failed"} 
[2025-08-01 10:26:14] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:14] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:14] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:14] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce1b2G96BU","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:14] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:15] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:17] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:17] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:17] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1hXWSNbf","event_type":"charge.failed"} 
[2025-08-01 10:26:17] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:17] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:17] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce1BvgDnQ0","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:17] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:18] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:20] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:21] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:21] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1jTjCNZB","event_type":"charge.failed"} 
[2025-08-01 10:26:21] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:21] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:21] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce1EGrp3JG","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:21] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 10:26:22] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_57d32ec6b148c6beba370aa684638920","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 10:26:24] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","error_code":"card_declined","error_message":"Your card has insufficient funds. Try a different card.","donation_type":"user_donation"} 
[2025-08-01 10:26:24] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:24] local.INFO: Received Stripe webhook event: charge.failed {"event_id":"evt_3RrGA5KQoAc42Xce1Bgy82Gz","event_type":"charge.failed"} 
[2025-08-01 10:26:24] local.INFO: Unhandled Stripe event: charge.failed  
[2025-08-01 10:26:24] local.INFO: Stripe webhook route hit  
[2025-08-01 10:26:24] local.INFO: Received Stripe webhook event: payment_intent.payment_failed {"event_id":"evt_3RrGA5KQoAc42Xce1dSVtclG","event_type":"payment_intent.payment_failed"} 
[2025-08-01 10:26:24] local.INFO: Payment intent failed recorded {"payment_intent_id":"pi_3RrGA5KQoAc42Xce1n4LEZst","failure_code":"card_declined","failure_message":"Your card has insufficient funds."} 
[2025-08-01 11:04:29] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrGlJKQoAc42Xce053PQJzk","idempotency_key":"payment_intent_a75291d1a9cec1cded700a69993d0242","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 11:04:29] local.INFO: Stripe webhook route hit  
[2025-08-01 11:04:30] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrGlJKQoAc42Xce0sWECNWf","event_type":"payment_intent.created"} 
[2025-08-01 11:04:30] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 11:04:32] local.INFO: Stripe webhook route hit  
[2025-08-01 11:04:32] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrGlJKQoAc42Xce0ZCh8SS2","event_type":"payment_intent.succeeded"} 
[2025-08-01 11:04:32] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrGlJKQoAc42Xce053PQJzk","amount":2500,"customer_id":null} 
[2025-08-01 11:04:32] local.INFO: Donation token marked as successful via webhook {"token":"iBVBsHYDLp6QKcGHtKvQnB3Xwdkzjh9C","payment_intent_id":"pi_3RrGlJKQoAc42Xce053PQJzk","amount":25} 
[2025-08-01 11:04:32] local.INFO: Stripe webhook route hit  
[2025-08-01 11:04:32] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrGlJKQoAc42Xce00aGnuX9","event_type":"charge.succeeded"} 
[2025-08-01 11:04:32] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrGlJKQoAc42Xce0ihsPn8P","object":"charge","amount":2500,"amount_captured":2500,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Quynn","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754046274,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Alias explicabo Vel","anonymous_to_all":"false","first_name":"Quynn","paymentFrequency":"one-time","donation_token":"iBVBsHYDLp6QKcGHtKvQnB3Xwdkzjh9C","anonymous_to_public":"false","idempotency_key":"payment_intent_a75291d1a9cec1cded700a69993d0242","address":"Laborum Aut exceptu","email":"<EMAIL>","state":"Dicta accusantium at","city":"Sequi Nam adipisicin","last_name":"Watts"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":57,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrGlJKQoAc42Xce053PQJzk","payment_method":"pm_1RrGlJKQoAc42XcezWubIUgL","payment_method_details":{"card":{"amount_authorized":2500,"authorization_code":"523072","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":4,"exp_year":2030,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":2500,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKMK-ssQGMgYRT_W0lEw6LBYb4toLpb7FGnncda_nR1V2pw8Lnf0ynZ5dZteQdKSbqEqxesouTPrkzWvg","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 11:04:32] local.INFO: metadata {"metadata":{"donated_to":"ezekiel-hardy","message":"Alias explicabo Vel","anonymous_to_all":"false","first_name":"Quynn","paymentFrequency":"one-time","donation_token":"iBVBsHYDLp6QKcGHtKvQnB3Xwdkzjh9C","anonymous_to_public":"false","idempotency_key":"payment_intent_a75291d1a9cec1cded700a69993d0242","address":"Laborum Aut exceptu","email":"<EMAIL>","state":"Dicta accusantium at","city":"Sequi Nam adipisicin","last_name":"Watts"}} 
[2025-08-01 11:04:32] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrGlJKQoAc42Xce0ihsPn8P","amount":25,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKMK-ssQGMgYRT_W0lEw6LBYb4toLpb7FGnncda_nR1V2pw8Lnf0ynZ5dZteQdKSbqEqxesouTPrkzWvg","receipt_number":null} 
[2025-08-01 11:04:33] local.INFO: Stripe webhook route hit  
[2025-08-01 11:04:33] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrGlJKQoAc42Xce0mufBteQ","event_type":"charge.updated"} 
[2025-08-01 11:04:33] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 11:04:38] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), 'ff10e141-ba74-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 11:04:44] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), '0dee28af-fe6a-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 11:05:33] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrGmKKQoAc42Xce0uHMZPlj","idempotency_key":"payment_intent_aebe1896a017020ba8e7d6832007dfa2","email":"<EMAIL>","amount":"50.00"} 
[2025-08-01 11:05:33] local.INFO: Stripe webhook route hit  
[2025-08-01 11:05:33] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrGmKKQoAc42Xce0HHRBJM4","event_type":"payment_intent.created"} 
[2025-08-01 11:05:33] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 11:05:35] local.INFO: Stripe webhook route hit  
[2025-08-01 11:05:35] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrGmKKQoAc42Xce0MIjxJF7","event_type":"payment_intent.succeeded"} 
[2025-08-01 11:05:35] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrGmKKQoAc42Xce0uHMZPlj","amount":5000,"customer_id":null} 
[2025-08-01 11:05:35] local.INFO: Donation token marked as successful via webhook {"token":"va2o0731GdL2qNlCUN4UwKWoIUoFaDVB","payment_intent_id":"pi_3RrGmKKQoAc42Xce0uHMZPlj","amount":50} 
[2025-08-01 11:05:35] local.INFO: Stripe webhook route hit  
[2025-08-01 11:05:35] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrGmKKQoAc42Xce0jTajNZ4","event_type":"charge.succeeded"} 
[2025-08-01 11:05:35] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrGmKKQoAc42Xce0TkutpCr","object":"charge","amount":5000,"amount_captured":5000,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Ivory","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754046337,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Consequatur non vel","anonymous_to_all":"true","first_name":"Ivory","paymentFrequency":"one-time","donation_token":"va2o0731GdL2qNlCUN4UwKWoIUoFaDVB","anonymous_to_public":"false","idempotency_key":"payment_intent_aebe1896a017020ba8e7d6832007dfa2","address":"Sunt et laboriosam","email":"<EMAIL>","state":"Similique veritatis","city":"Dolorem nisi ullamco","last_name":"French"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":24,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrGmKKQoAc42Xce0uHMZPlj","payment_method":"pm_1RrGmLKQoAc42XceMdeab0AR","payment_method_details":{"card":{"amount_authorized":5000,"authorization_code":"780013","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2030,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":5000,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKIK_ssQGMgb4_2M4y746LBavMqmWCcSSHv2luxYVDT4xzrlSHkn55SBnb-P6_aVl-PRz78_tcPiDoFhG","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 11:05:35] local.WARNING: Charge succeeded but no donor found {"charge_id":"ch_3RrGmKKQoAc42Xce0TkutpCr","customer_id":null,"amount":5000} 
[2025-08-01 11:05:37] local.INFO: Stripe webhook route hit  
[2025-08-01 11:05:37] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrGmKKQoAc42Xce0YSfVjnp","event_type":"charge.updated"} 
[2025-08-01 11:05:37] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 11:05:41] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '89dbeb1f-cd71-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 11:05:45] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), 'b1f018b7-b91a-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 11:07:50] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RrGoYKQoAc42Xce0R8NmdIx","idempotency_key":"payment_intent_378c80acfd693f2ecda5fce81074a364","email":"<EMAIL>","amount":"25.00"} 
[2025-08-01 11:07:50] local.INFO: Stripe webhook route hit  
[2025-08-01 11:07:50] local.INFO: Received Stripe webhook event: payment_intent.created {"event_id":"evt_3RrGoYKQoAc42Xce00Hed1y7","event_type":"payment_intent.created"} 
[2025-08-01 11:07:50] local.INFO: Unhandled Stripe event: payment_intent.created  
[2025-08-01 11:07:53] local.INFO: Stripe webhook route hit  
[2025-08-01 11:07:53] local.INFO: Received Stripe webhook event: payment_intent.succeeded {"event_id":"evt_3RrGoYKQoAc42Xce0KD4lP3n","event_type":"payment_intent.succeeded"} 
[2025-08-01 11:07:53] local.INFO: Payment intent succeeded {"payment_intent_id":"pi_3RrGoYKQoAc42Xce0R8NmdIx","amount":2500,"customer_id":null} 
[2025-08-01 11:07:53] local.INFO: Donation token marked as successful via webhook {"token":"JG5EcYosYTOt1Po4WQTBFTUCoAEykBC2","payment_intent_id":"pi_3RrGoYKQoAc42Xce0R8NmdIx","amount":25} 
[2025-08-01 11:07:53] local.INFO: Stripe webhook route hit  
[2025-08-01 11:07:53] local.INFO: Received Stripe webhook event: charge.succeeded {"event_id":"evt_3RrGoYKQoAc42Xce0d730Zk2","event_type":"charge.succeeded"} 
[2025-08-01 11:07:53] local.INFO: charge:  {"charge":{"Stripe\\Charge":{"id":"ch_3RrGoYKQoAc42Xce0VS3A3ee","object":"charge","amount":2500,"amount_captured":2500,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":"12345","state":null},"email":"<EMAIL>","name":"Dalton","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1754046475,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"donated_to":"ezekiel-hardy","message":"Praesentium ullam pr","anonymous_to_all":"false","first_name":"Dalton","paymentFrequency":"one-time","donation_token":"JG5EcYosYTOt1Po4WQTBFTUCoAEykBC2","anonymous_to_public":"true","idempotency_key":"payment_intent_378c80acfd693f2ecda5fce81074a364","address":"Et fugiat dolor quo","state":"Facere sint inventor","email":"<EMAIL>","city":"Vitae numquam debiti","last_name":"Hill"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":63,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrGoYKQoAc42Xce0R8NmdIx","payment_method":"pm_1RrGoYKQoAc42Xceuax2N9Eg","payment_method_details":{"card":{"amount_authorized":2500,"authorization_code":"121548","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":"pass","cvc_check":"pass"},"country":"US","exp_month":11,"exp_year":2030,"extended_authorization":{"status":"disabled"},"fingerprint":"Ewcp0phqWRy8g3u5","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"691199911248112","overcapture":{"maximum_amount_capturable":2500,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":"<EMAIL>","receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKIvAssQGMgZMG80CFhk6LBZscMix_TWpxGLrnFCACDnbMTV4UqPFJ7D8tK6foxjVorxBz6Md1MdvTJWT","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-08-01 11:07:53] local.INFO: metadata {"metadata":{"donated_to":"ezekiel-hardy","message":"Praesentium ullam pr","anonymous_to_all":"false","first_name":"Dalton","paymentFrequency":"one-time","donation_token":"JG5EcYosYTOt1Po4WQTBFTUCoAEykBC2","anonymous_to_public":"true","idempotency_key":"payment_intent_378c80acfd693f2ecda5fce81074a364","address":"Et fugiat dolor quo","state":"Facere sint inventor","email":"<EMAIL>","city":"Vitae numquam debiti","last_name":"Hill"}} 
[2025-08-01 11:07:53] local.INFO: Charge succeeded and stored {"charge_id":"ch_3RrGoYKQoAc42Xce0VS3A3ee","amount":25,"customer_id":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTVpVVdLUW9BYzQyWGNlKIvAssQGMgZMG80CFhk6LBZscMix_TWpxGLrnFCACDnbMTV4UqPFJ7D8tK6foxjVorxBz6Md1MdvTJWT","receipt_number":null} 
[2025-08-01 11:07:55] local.INFO: Stripe webhook route hit  
[2025-08-01 11:07:55] local.INFO: Received Stripe webhook event: charge.updated {"event_id":"evt_3RrGoYKQoAc42Xce0fcC5hu7","event_type":"charge.updated"} 
[2025-08-01 11:07:55] local.INFO: Unhandled Stripe event: charge.updated  
[2025-08-01 11:08:01] local.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php:91)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\Stream\\AbstractStream.php(91): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(350): Symfony\\Component\\Mailer\\Transport\\Smtp\\Stream\\AbstractStream->readLine()
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(197): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->getFullResponse()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RSET\\r\\n', Array)
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(228): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RSET\\r\\n', Array)
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-paym...', Array, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewPaymentRecieved))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '621a3c88-58b4-4...', Object(App\\Notifications\\NewPaymentRecieved), 'mail')
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewPaymentRecieved), Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#56 {main}
"} 
[2025-08-01 11:08:05] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.thanks-r...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\Donor), Object(App\\Notifications\\ThanksReceipt))
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\Donor), '01deb546-10b4-4...', Object(App\\Notifications\\ThanksReceipt), 'mail')
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(54): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\SendQueuedNotifications.php(118): Illuminate\\Notifications\\ChannelManager->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\ThanksReceipt), Array)
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Notifications\\SendQueuedNotifications->handle(Object(Illuminate\\Notifications\\ChannelManager))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Notifications\\SendQueuedNotifications), false)
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Notifications\\SendQueuedNotifications))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Notifications\\SendQueuedNotifications))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-08-01 11:23:01] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php(206): Illuminate\\Mail\\Mailer->send(Object(Closure), Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Mail\\Mailable->Illuminate\\Mail\\{closure}()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php(199): Illuminate\\Mail\\Mailable->withLocale(NULL, Object(Closure))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(353): Illuminate\\Mail\\Mailable->send(Object(Illuminate\\Mail\\Mailer))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(300): Illuminate\\Mail\\Mailer->sendMailable(Object(App\\Mail\\ReplyToUser))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\PendingMail.php(123): Illuminate\\Mail\\Mailer->send(Object(App\\Mail\\ReplyToUser))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\app\\Listeners\\SendReplyMail.php(28): Illuminate\\Mail\\PendingMail->send(Object(App\\Mail\\ReplyToUser))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\CallQueuedListener.php(113): App\\Listeners\\SendReplyMail->handle(Object(App\\Events\\SendReplyToMessage))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Events\\CallQueuedListener->handle(Object(Illuminate\\Foundation\\Application))
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Events\\CallQueuedListener), false)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Events\\CallQueuedListener))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#51 {main}
"} 
[2025-08-01 11:23:40] local.ERROR: Failed to authenticate on SMTP server with username "41fc64547a6b9f" using the following authenticators: "CRAM-MD5", "LOGIN", "PLAIN". Authenticator "CRAM-MD5" returned "Expected response code "235" but got code "535", with message "535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing".". Authenticator "LOGIN" returned "Expected response code "334" but got empty code.". Authenticator "PLAIN" returned "Expected response code "235" but got empty code.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\TransportException(code: 535): Failed to authenticate on SMTP server with username \"41fc64547a6b9f\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https://mailtrap.io/billing/plans/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\". at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php:247)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(177): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->handleAuth(Array)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(134): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->doEhloCommand()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(255): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('HELO localhost\\r...', Array)
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(281): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doHeloCommand()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(211): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->start()
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php(206): Illuminate\\Mail\\Mailer->send(Object(Closure), Array, Object(Closure))
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Mail\\Mailable->Illuminate\\Mail\\{closure}()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php(199): Illuminate\\Mail\\Mailable->withLocale(NULL, Object(Closure))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(353): Illuminate\\Mail\\Mailable->send(Object(Illuminate\\Mail\\Mailer))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(300): Illuminate\\Mail\\Mailer->sendMailable(Object(App\\Mail\\ReplyToUser))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\PendingMail.php(123): Illuminate\\Mail\\Mailer->send(Object(App\\Mail\\ReplyToUser))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\app\\Listeners\\SendReplyMail.php(28): Illuminate\\Mail\\PendingMail->send(Object(App\\Mail\\ReplyToUser))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\CallQueuedListener.php(113): App\\Listeners\\SendReplyMail->handle(Object(App\\Events\\SendReplyToMessage))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Events\\CallQueuedListener->handle(Object(Illuminate\\Foundation\\Application))
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(125): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(129): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Events\\CallQueuedListener), false)
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Events\\CallQueuedListener))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Events\\CallQueuedListener))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(391): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(334): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#51 {main}
"} 
[2025-08-01 12:12:25] local.INFO: Stripe webhook route hit  
[2025-08-01 12:12:25] local.INFO: Received Stripe webhook event: invoice.upcoming {"event_id":"evt_1RrHp2KQoAc42Xceu2gAT9Xt","event_type":"invoice.upcoming"} 
[2025-08-01 12:12:25] local.INFO: Unhandled Stripe event: invoice.upcoming  
[2025-08-07 05:32:21] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-07 05:52:14] local.INFO: Created new payment intent {"payment_intent_id":"pi_3RtMkMKQoAc42Xce12RsJwj1","idempotency_key":"payment_intent_fa5e7ab406936a8859f5377755d9e61f","email":"<EMAIL>","amount":"200.00"} 
[2025-08-07 09:40:43] local.ERROR: The process "C:\xampp\php\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1" exceeded the timeout of 60 seconds. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\ProcessTimedOutException(code: 0): The process \"C:\\xampp\\php\\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1\" exceeded the timeout of 60 seconds. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\process\\Process.php:1181)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\process\\Process.php(450): Symfony\\Component\\Process\\Process->checkTimeout()
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\process\\Process.php(251): Symfony\\Component\\Process\\Process->wait()
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(180): Symfony\\Component\\Process\\Process->run(Object(Closure))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(91): Illuminate\\Queue\\Listener->runProcess(Object(Symfony\\Component\\Process\\Process), '128')
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListenCommand.php(74): Illuminate\\Queue\\Listener->listen(NULL, 'default', Object(Illuminate\\Queue\\ListenerOptions))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListenCommand->handle()
#6 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListenCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 
[2025-08-08 06:37:15] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-08 06:38:56] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-08 06:47:04] local.ERROR: Undefined property: Illuminate\Mail\Message::$name (View: C:\Users\<USER>\ASFL-Gauntlet\resources\views\emails\new-message-notification.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Undefined property: Illuminate\\Mail\\Message::$name (View: C:\\Users\\<USER>\\ASFL-Gauntlet\\resources\\views\\emails\\new-message-notification.blade.php) at C:\\Users\\<USER>\\ASFL-Gauntlet\\storage\\framework\\views\\f45d4f269e779dd7f82df50fdfdedf48.php:134)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ErrorException), 1)
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(ErrorException), 1)
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#7 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#8 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(444): Illuminate\\View\\View->render()
#9 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(419): Illuminate\\Mail\\Mailer->renderView('emails.new-mess...', Array)
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(312): Illuminate\\Mail\\Mailer->addContent(Object(Illuminate\\Mail\\Message), 'emails.new-mess...', NULL, NULL, Array)
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-mess...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewMessageNotification))
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '4fbe5ad7-2e3b-4...', Object(App\\Notifications\\NewMessageNotification), 'mail')
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(78): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewMessageNotification))
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(39): Illuminate\\Notifications\\NotificationSender->send(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewMessageNotification))
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php(18): Illuminate\\Notifications\\ChannelManager->send(Object(App\\Models\\User), Object(App\\Notifications\\NewMessageNotification))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\app\\Http\\Controllers\\HomePageController.php(234): App\\Models\\User->notify(Object(App\\Notifications\\NewMessageNotification))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HomePageController->storeMessage(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomePageController), 'storeMessage')
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\ASFL-Gauntlet\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Mail\\Message::$name at C:\\Users\\<USER>\\ASFL-Gauntlet\\storage\\framework\\views\\f45d4f269e779dd7f82df50fdfdedf48.php:134)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\storage\\framework\\views\\f45d4f269e779dd7f82df50fdfdedf48.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined prope...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Cybertron\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#10 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#11 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(444): Illuminate\\View\\View->render()
#12 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(419): Illuminate\\Mail\\Mailer->renderView('emails.new-mess...', Array)
#13 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(312): Illuminate\\Mail\\Mailer->addContent(Object(Illuminate\\Mail\\Message), 'emails.new-mess...', NULL, NULL, Array)
#14 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Channels\\MailChannel.php(66): Illuminate\\Mail\\Mailer->send('emails.new-mess...', Array, Object(Closure))
#15 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(147): Illuminate\\Notifications\\Channels\\MailChannel->send(Object(App\\Models\\User), Object(App\\Notifications\\NewMessageNotification))
#16 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(105): Illuminate\\Notifications\\NotificationSender->sendToNotifiable(Object(App\\Models\\User), '4fbe5ad7-2e3b-4...', Object(App\\Notifications\\NewMessageNotification), 'mail')
#17 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Notifications\\NotificationSender->Illuminate\\Notifications\\{closure}()
#18 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(100): Illuminate\\Notifications\\NotificationSender->withLocale(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php(78): Illuminate\\Notifications\\NotificationSender->sendNow(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewMessageNotification))
#20 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php(39): Illuminate\\Notifications\\NotificationSender->send(Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Notifications\\NewMessageNotification))
#21 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php(18): Illuminate\\Notifications\\ChannelManager->send(Object(App\\Models\\User), Object(App\\Notifications\\NewMessageNotification))
#22 C:\\Users\\<USER>\\ASFL-Gauntlet\\app\\Http\\Controllers\\HomePageController.php(234): App\\Models\\User->notify(Object(App\\Notifications\\NewMessageNotification))
#23 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HomePageController->storeMessage(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomePageController), 'storeMessage')
#25 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#26 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#27 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\ASFL-Gauntlet\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-27 07:08:51] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
