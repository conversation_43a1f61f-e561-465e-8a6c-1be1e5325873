<?php

namespace App\Http\Controllers;

use Illuminate\View\View;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\HomePageService;
use App\Models\Message;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;


class HomePageController extends Controller
{

    protected $homePageService;


    public function __construct(HomePageService $homePageService)
    {

        $this->homePageService = $homePageService;
    }
    public function home(): View
    {
        try {

            $data = $this->homePageService->getHomePageData();
            return view('home.home', compact('data'));
        } catch (\Exception $e) {
            return view('errors.500');
        }
    }


    public function homePageData(): JsonResponse
    {
        try {
            $data = $this->homePageService->getHomePageData();

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);
        } catch (\Throwable $e) {

            Log::error('HomePageData Error: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while loading home page data.',
            ], 500);
        }
    }

    public function leaderBoard(Request $request): View
    {
        try {
            return view('home.leaderboard');
        } catch (\Exception $e) {
            return view('Errors.errors500');
        }
    }







    public function leaderBoardData(Request $request)
    {
        $donationSubquery = DB::table('user_donors')
            ->select('user_id', DB::raw('SUM(amount) as total_collected'))
            ->groupBy('user_id');

        $query = User::query()
            ->select(
                'users.id',
                'users.name',
                'users.city',
                'users.slug',
                'users.fundraising_goal',
                'users.profile_photo',
                DB::raw('COALESCE(donations.total_collected, 0) as total_collected')
            )
            ->leftJoinSub($donationSubquery, 'donations', 'users.id', '=', 'donations.user_id')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('role_user')
                    ->join('roles', 'role_user.role_id', '=', 'roles.id')
                    ->where('roles.name', 'user')
                    ->whereColumn('role_user.user_id', 'users.id');
            });

        if ($request->filled('search')) {
            $search = trim($request->search);
            $query->where(function ($q) use ($search) {
                $q->where('users.name', 'like', "%{$search}%")
                    ->orWhere('users.city', 'like', "%{$search}%");
            });
        }

        $validSortColumns = ['total_collected', 'name', 'city'];

        $sortBy = in_array($request->input('sortBy'), $validSortColumns)
            ? $request->input('sortBy')
            : 'total_collected';

        $sortOrder = in_array(strtolower($request->input('sortOrder')), ['asc', 'desc'])
            ? strtolower($request->input('sortOrder'))
            : ($sortBy === 'total_collected' ? 'desc' : 'asc');

        // Add stable sorting to prevent duplicates across pages
        $query = $query->orderBy($sortBy, $sortOrder);

        // Add secondary sort by ID to ensure stable ordering when primary sort values are equal
        if ($sortBy !== 'users.id') {
            $query = $query->orderBy('users.id', 'asc');
        }

        $fundraisers = $query->paginate(16);

        $fundraisers->setCollection(
            $fundraisers->getCollection()->transform(function ($user) {
                $user->donationPageUrl = route('donationPage', ['slug' => $user->slug]);
                $user->profile_photo = $user->profile_photo
                    ? Storage::url($user->profile_photo)
                    : Storage::url('uploads/default.png');
                return $user;
            })
        );

        return response()->json($fundraisers);
    }


    public function donate(): View
    {
        try {
            return view('home.donate');
        } catch (\Exception $e) {
            return view('Errors.errors500');
        }
    }

    public function contact(): View
    {
        try {

            $faqs = DB::table('faqs')->orderBy('order')->get();
            return view('home.contact', compact('faqs'));
        } catch (\Exception $e) {
            return view('Errors.errors500');
        }
    }

    public function gauntlet(): View
    {
        try {
            return view('home.gauntlet');
        } catch (\Exception $e) {
            return view('Errors.errors500');
        }
    }

    public function about(): View
    {
        try {
            return view('home.about');
        } catch (\Exception $e) {
            return view('Errors.errors500');
        }
    }


    public function athletes($search)
    {
        $users = User::whereHas('roles', function ($query) {
            $query->where('name', 'user');
        })
            ->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('city', 'like', "%{$search}%");
            })
            ->paginate(12);

        $users->getCollection()->transform(function ($user) {
            $user->donationPageUrl = route('donationPage', ['slug' => $user->slug]);

            $user->profile_photo = $user->profile_photo
                ? Storage::url($user->profile_photo)
                : Storage::url('uploads/default.png');

            $user->total_collected = $user->donors()->sum('amount');

            return $user;
        });

        return response()->json($users);
    }


    public function storeMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'message' => 'required|string|max:10000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }


        $message = Message::create($validator->validated());

        // send mail to admin
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        foreach ($admins as $admin) {
            $admin->notify(new \App\Notifications\NewMessageNotification($message));
        }
        return response()->json([
            'success' => true,
            'message' => 'Message stored successfully',
        ], 201);
    }
}
