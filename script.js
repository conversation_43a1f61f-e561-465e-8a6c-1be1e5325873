// Global variables
let documentationData = null;
let currentTheme = localStorage.getItem('theme') || 'light';
let searchIndex = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    // Show loading screen
    showLoadingScreen();

    // Load documentation data
    await loadDocumentationData();

    // Initialize components
    initializeTheme();
    initializeNavigation();
    initializeSearch();
    initializeScrollEffects();
    initializeAnimations();

    // Hide loading screen
    hideLoadingScreen();
}

function showLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    loadingScreen.style.display = 'flex';
}

function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }, 1500);
}

// Load and parse documentation data
async function loadDocumentationData() {
    try {
        const response = await fetch('PROJECT_DOCUMENTATION.md');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const markdown = await response.text();

        if (markdown.trim().length === 0) {
            throw new Error('Empty markdown file');
        }

        documentationData = parseMarkdown(markdown);

        if (!documentationData || documentationData.length === 0) {
            throw new Error('No sections parsed from markdown');
        }

        generateTableOfContents();
        renderDocumentation();
        buildSearchIndex();

        console.log('Successfully loaded documentation with', documentationData.length, 'sections');
    } catch (error) {
        console.error('Error loading documentation:', error);
        console.log('Loading fallback content...');
        // Fallback to static content if markdown file is not available
        loadFallbackContent();
    }
}

function parseMarkdown(markdown) {
    const lines = markdown.split('\n');
    const sections = [];
    let currentSection = null;
    let currentSubsection = null;
    let inCodeBlock = false;

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();

        // Handle code blocks
        if (trimmedLine.startsWith('```')) {
            inCodeBlock = !inCodeBlock;
            const target = currentSubsection || currentSection;
            if (target) {
                target.content.push(line);
            }
            return;
        }

        // If we're in a code block, just add the line
        if (inCodeBlock) {
            const target = currentSubsection || currentSection;
            if (target) {
                target.content.push(line);
            }
            return;
        }

        // Main headers (##) - but not table of contents
        if (trimmedLine.startsWith('## ') && !trimmedLine.toLowerCase().includes('table of contents')) {
            if (currentSection) {
                sections.push(currentSection);
            }
            const title = trimmedLine.substring(3).trim();
            currentSection = {
                id: generateId(title),
                title: title,
                content: [],
                subsections: []
            };
            currentSubsection = null;
        }
        // Sub headers (###)
        else if (trimmedLine.startsWith('### ')) {
            if (currentSection) {
                const title = trimmedLine.substring(4).trim();
                currentSubsection = {
                    id: generateId(title),
                    title: title,
                    content: []
                };
                currentSection.subsections.push(currentSubsection);
            }
        }
        // Sub-sub headers (####) - treat as content with special formatting
        else if (trimmedLine.startsWith('#### ')) {
            const target = currentSubsection || currentSection;
            if (target) {
                target.content.push(line);
            }
        }
        // Regular content lines (including empty lines for spacing)
        else {
            const target = currentSubsection || currentSection;
            if (target) {
                target.content.push(line);
            }
        }
    });

    if (currentSection) {
        sections.push(currentSection);
    }

    // Filter out empty sections and clean up content
    const filteredSections = sections.filter(section => {
        // Clean up content arrays by removing leading/trailing empty lines
        section.content = cleanContentArray(section.content);
        section.subsections = section.subsections.filter(subsection => {
            subsection.content = cleanContentArray(subsection.content);
            return subsection.content.length > 0;
        });

        return section.content.length > 0 || section.subsections.length > 0;
    });

    console.log('Parsed sections:', filteredSections.map(s => s.title));
    return filteredSections;
}

function cleanContentArray(contentArray) {
    // Remove leading empty lines
    while (contentArray.length > 0 && contentArray[0].trim() === '') {
        contentArray.shift();
    }

    // Remove trailing empty lines
    while (contentArray.length > 0 && contentArray[contentArray.length - 1].trim() === '') {
        contentArray.pop();
    }

    return contentArray;
}

function generateId(title) {
    return title.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

function generateTableOfContents() {
    const tocElement = document.getElementById('toc');
    if (!documentationData || documentationData.length === 0) {
        console.warn('No documentation data available for TOC generation');
        tocElement.innerHTML = '<p>Loading table of contents...</p>';
        return;
    }

    console.log('Generating TOC for', documentationData.length, 'sections');

    const tocHTML = documentationData.map(section => {
        const subsectionsHTML = section.subsections.map(subsection =>
            `<li><a href="#${subsection.id}" class="toc-link" data-section="${subsection.id}">${subsection.title}</a></li>`
        ).join('');

        return `
            <li>
                <a href="#${section.id}" class="toc-link" data-section="${section.id}">${section.title}</a>
                ${subsectionsHTML ? `<ul>${subsectionsHTML}</ul>` : ''}
            </li>
        `;
    }).join('');

    tocElement.innerHTML = `<ul>${tocHTML}</ul>`;

    // Add click handlers
    document.querySelectorAll('.toc-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            scrollToSection(sectionId);
        });
    });

    console.log('TOC generated successfully');
}

function renderDocumentation() {
    const contentElement = document.getElementById('documentation-content');
    if (!documentationData || documentationData.length === 0) {
        console.warn('No documentation data available for rendering');
        contentElement.innerHTML = '<div class="loading-message"><p>Loading documentation content...</p></div>';
        return;
    }

    console.log('Rendering documentation with', documentationData.length, 'sections');

    const contentHTML = documentationData.map(section => {
        const subsectionsHTML = section.subsections.map(subsection => `
            <div class="subsection" id="${subsection.id}">
                <h3>${subsection.title}</h3>
                <div class="subsection-content">
                    ${formatContent(subsection.content)}
                </div>
            </div>
        `).join('');

        return `
            <section class="doc-section" id="${section.id}">
                <div class="section-header">
                    <h2>${section.title}</h2>
                    <div class="section-actions">
                        <button class="copy-link" data-section="${section.id}" title="Copy link">
                            <i class="fas fa-link"></i>
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    ${formatContent(section.content)}
                    ${subsectionsHTML}
                </div>
            </section>
        `;
    }).join('');

    contentElement.innerHTML = contentHTML;

    // Add copy link functionality
    document.querySelectorAll('.copy-link').forEach(button => {
        button.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section');
            const url = `${window.location.origin}${window.location.pathname}#${sectionId}`;
            navigator.clipboard.writeText(url).then(() => {
                showNotification('Link copied to clipboard!');
            });
        });
    });

    console.log('Documentation rendered successfully');
}

function formatContent(contentLines) {
    let html = '';
    let inCodeBlock = false;
    let inList = false;
    let listType = '';

    contentLines.forEach(line => {
        const trimmedLine = line.trim();

        // Code blocks
        if (trimmedLine.startsWith('```')) {
            if (inCodeBlock) {
                html += '</code></pre>';
                inCodeBlock = false;
            } else {
                const language = trimmedLine.substring(3) || 'text';
                html += `<pre><code class="language-${language}">`;
                inCodeBlock = true;
            }
            return;
        }

        if (inCodeBlock) {
            html += line + '\n';
            return;
        }

        // Lists
        if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ')) {
            if (!inList) {
                html += '<ul>';
                inList = true;
                listType = 'ul';
            } else if (listType === 'ol') {
                html += '</ol><ul>';
                listType = 'ul';
            }
            html += `<li>${trimmedLine.substring(2)}</li>`;
            return;
        }

        if (/^\d+\.\s/.test(trimmedLine)) {
            if (!inList) {
                html += '<ol>';
                inList = true;
                listType = 'ol';
            } else if (listType === 'ul') {
                html += '</ul><ol>';
                listType = 'ol';
            }
            html += `<li>${trimmedLine.replace(/^\d+\.\s/, '')}</li>`;
            return;
        }

        // Close lists if not continuing
        if (inList && !trimmedLine.startsWith('- ') && !trimmedLine.startsWith('* ') && !/^\d+\.\s/.test(trimmedLine)) {
            html += listType === 'ul' ? '</ul>' : '</ol>';
            inList = false;
            listType = '';
        }

        // Headers
        if (trimmedLine.startsWith('####')) {
            html += `<h4>${trimmedLine.substring(5)}</h4>`;
        } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
            html += `<p><strong>${trimmedLine.slice(2, -2)}</strong></p>`;
        } else if (trimmedLine) {
            html += `<p>${formatInlineElements(trimmedLine)}</p>`;
        }
    });

    // Close any open lists
    if (inList) {
        html += listType === 'ul' ? '</ul>' : '</ol>';
    }

    return html;
}

function formatInlineElements(text) {
    // Bold text
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic text
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Inline code
    text = text.replace(/`(.*?)`/g, '<code>$1</code>');

    // Links
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

    return text;
}

function loadFallbackContent() {
    // Comprehensive fallback content structure
    documentationData = [
        {
            id: 'project-overview',
            title: 'Project Overview',
            content: [
                'ASFL Gauntlet is more than just a fundraising platform—it\'s a digital embodiment of hope, community, and the relentless fight against cancer. Built with Laravel 11, this comprehensive web application serves as the technological backbone for the "A Shot For Life Gauntlet," a grueling 24-hour basketball marathon that symbolizes the endurance and determination of cancer patients in their battle against the disease.',
                '',
                'Since 2017, this event has brought together hundreds of players who don the exclusive black and white ASFL Gauntlet jerseys, playing continuously for 24 hours with the cancer community metaphorically on their backs. The platform transforms this physical event into a digital fundraising powerhouse, enabling participants to create personalized fundraising profiles, collect donations from supporters worldwide, and track their progress toward ambitious fundraising goals.'
            ],
            subsections: [
                {
                    id: 'key-technologies',
                    title: 'Key Technologies',
                    content: [
                        '- **Backend**: Laravel 11 (PHP 8.2+)',
                        '- **Frontend**: Bootstrap 5, JavaScript ES6',
                        '- **Database**: MySQL',
                        '- **Payment**: Stripe Integration',
                        '- **Email**: Laravel Mail with SMTP',
                        '- **File Storage**: Laravel Storage (Local/S3)',
                        '- **Real-time**: Livewire Components'
                    ]
                }
            ]
        },
        {
            id: 'the-asfl-gauntlet-story',
            title: 'The ASFL Gauntlet Story',
            content: [
                'The A Shot For Life Gauntlet is a 24-hour marathon basketball event that serves as a powerful symbol of the struggle that cancer patients endure in their fight against the disease. Since 2017, this extraordinary event has taken place at the Starland Sportsplex, where hundreds of players of all ages and skill levels come together for a common cause.'
            ],
            subsections: [
                {
                    id: 'what-is-the-gauntlet',
                    title: 'What is the Gauntlet?',
                    content: [
                        'The event features multiple courts running simultaneously:',
                        '- **Intense Floor**: For highly competitive players',
                        '- **Moderate Floor**: For recreational players',
                        '- **Casual Floor**: For beginners and families',
                        '- **Beginner Floor**: For those new to basketball',
                        '',
                        'Each participant wears the exclusive ASFL Gauntlet black and white jersey, symbolically carrying the cancer community on their back as they play through the night and into the next day.'
                    ]
                }
            ]
        },
        {
            id: 'page-by-page-guide',
            title: 'Page-by-Page Guide',
            content: [
                'This comprehensive guide covers every page and feature of the ASFL Gauntlet platform, explaining both the user experience and technical implementation.'
            ],
            subsections: [
                {
                    id: 'homepage',
                    title: '🏠 Homepage (/) - The Digital Front Door',
                    content: [
                        'The homepage serves as the welcoming entry point to the ASFL Gauntlet community. It\'s designed to immediately communicate the platform\'s purpose while showcasing the collective impact of all fundraisers.',
                        '',
                        '**Key Features:**',
                        '- **Hero Section**: Features the ASFL Gauntlet logo and mission statement',
                        '- **Live Statistics**: Real-time display of total funds raised, number of participants, and progress toward the annual goal',
                        '- **Progress Bar**: Visual representation of how close the community is to reaching their collective fundraising target',
                        '- **Top Fundraisers Carousel**: Showcases the top 5 performing fundraisers with their photos, names, and current totals',
                        '- **Recent Donors**: Displays the latest 5 donations to create a sense of active community participation',
                        '- **Call-to-Action Buttons**: Prominent "Register" and "Donate" buttons to guide user actions',
                        '',
                        '**Technical Implementation:**',
                        '- Uses `HomePageService` to aggregate data from multiple sources',
                        '- JavaScript-powered progress bar that animates based on real donation totals',
                        '- Responsive design that works seamlessly on mobile and desktop',
                        '- Real-time data updates via AJAX calls to `/home-data` endpoint'
                    ]
                },
                {
                    id: 'leaderboard',
                    title: '📊 Leaderboard (/leaderboard) - The Competition Hub',
                    content: [
                        'The leaderboard transforms fundraising into a friendly competition, motivating participants while celebrating their achievements.',
                        '',
                        '**Key Features:**',
                        '- **Sortable Rankings**: Users can sort by total raised, fundraising goal, or alphabetically',
                        '- **Pagination**: Handles large numbers of fundraisers efficiently (16 per page)',
                        '- **Search Functionality**: Allows visitors to find specific fundraisers by name or city',
                        '- **Profile Links**: Each fundraiser entry links directly to their donation page',
                        '- **Progress Indicators**: Visual bars showing each fundraiser\'s progress toward their goal',
                        '- **Responsive Grid**: Adapts from 4 columns on desktop to single column on mobile',
                        '',
                        '**Technical Implementation:**',
                        '- AJAX-powered sorting and filtering for smooth user experience',
                        '- Stable sorting algorithm prevents duplicate entries across pages',
                        '- Optimized database queries with proper indexing for fast load times',
                        '- Real-time data synchronization with donation updates'
                    ]
                },
                {
                    id: 'about-page',
                    title: 'ℹ️ About Page (/about) - The Story Behind the Mission',
                    content: [
                        'The about page provides context and emotional connection to the cause, explaining the significance of the Gauntlet event.',
                        '',
                        '**Key Features:**',
                        '- **Hero Image**: Powerful visual representation of the basketball event',
                        '- **Mission Statement**: Clear explanation of what the Gauntlet represents',
                        '- **Event History**: Background on the event\'s origins since 2017',
                        '- **Participation Details**: Information about different skill levels and court types',
                        '- **Community Impact**: Stories and statistics about the event\'s effect on cancer research'
                    ]
                },
                {
                    id: 'contact-page',
                    title: '📞 Contact Page (/contact) - Community Support Hub',
                    content: [
                        'The contact page facilitates communication between visitors and administrators while providing helpful information.',
                        '',
                        '**Key Features:**',
                        '- **Contact Form**: Simple three-field form (name, email, message) for inquiries',
                        '- **FAQ Section**: Dynamically loaded frequently asked questions from the database',
                        '- **Real-time Validation**: Client-side form validation with immediate feedback',
                        '- **Success Confirmation**: Modal popup confirming message submission',
                        '- **Admin Notification**: Automatic email alerts to administrators for new messages',
                        '',
                        '**Technical Implementation:**',
                        '- AJAX form submission prevents page reload',
                        '- Server-side validation with detailed error messages',
                        '- Automatic email notifications using Laravel\'s mail system',
                        '- FAQ content managed through database for easy updates'
                    ]
                },
                {
                    id: 'donation-pages',
                    title: '👤 Individual Donation Pages (/user/{slug}) - Personal Fundraising Profiles',
                    content: [
                        'Each fundraiser gets their own personalized donation page that tells their unique story and facilitates contributions.',
                        '',
                        '**Key Features:**',
                        '- **Personal Hero Section**: Large profile photo and customized "Support [Name]\'s Fundraiser" header',
                        '- **Progress Tracking**: Visual progress bar and statistics showing raised amount vs. goal',
                        '- **Personal Message**: Fundraiser\'s custom message explaining their connection to the cause',
                        '- **Donation Tiers**: Multiple preset donation amounts with descriptive labels:',
                        '  - $25 - Supporter (Basic Donation)',
                        '  - $50 - Advocate (Standard Support)',
                        '  - $100 - Champion (Strong Support)',
                        '  - $250 - Hero (Major Impact)',
                        '  - $500 - Legend (Significant Contribution)',
                        '- **Custom Amount Option**: Input field for donors who want to give a specific amount',
                        '- **Secure Checkout**: Integrated Stripe payment processing with donation tokens for security',
                        '- **Social Sharing**: Easy sharing options for social media platforms',
                        '',
                        '**Technical Implementation:**',
                        '- Unique slug-based URLs for each fundraiser (e.g., `/user/john-smith`)',
                        '- Donation token system prevents payment replay attacks',
                        '- Real-time progress updates after successful donations',
                        '- Responsive design optimized for mobile sharing'
                    ]
                }
            ]
        },
        {
            id: 'system-architecture',
            title: 'System Architecture',
            content: [
                'The ASFL Gauntlet platform is built on a robust, scalable architecture that ensures reliability, security, and performance.'
            ],
            subsections: [
                {
                    id: 'mvc-architecture',
                    title: 'MVC Architecture',
                    content: [
                        'The application follows Laravel\'s Model-View-Controller pattern:',
                        '',
                        '```',
                        '┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐',
                        '│     Models      │    │   Controllers   │    │     Views       │',
                        '│                 │    │                 │    │                 │',
                        '│ • User          │◄──►│ • AdminController│◄──►│ • Admin Dashboard│',
                        '│ • Donor         │    │ • UserController │    │ • User Profile  │',
                        '│ • Message       │    │ • PaymentController│  │ • Donation Pages│',
                        '│ • Transaction   │    │ • MessageController│  │ • Email Templates│',
                        '└─────────────────┘    └─────────────────┘    └─────────────────┘',
                        '```'
                    ]
                }
            ]
        },
        {
            id: 'installation-guide',
            title: 'Installation Guide',
            content: [
                'Follow these comprehensive steps to set up the ASFL Gauntlet platform on your local development environment.'
            ],
            subsections: [
                {
                    id: 'prerequisites',
                    title: 'Prerequisites',
                    content: [
                        '- PHP 8.2 or higher',
                        '- Composer',
                        '- Node.js & NPM',
                        '- MySQL 8.0+',
                        '- Web server (Apache/Nginx)'
                    ]
                },
                {
                    id: 'installation-steps',
                    title: 'Installation Steps',
                    content: [
                        '**Step 1: Clone Repository**',
                        '```bash',
                        'git clone <repository-url>',
                        'cd ASFL-Gauntlet',
                        '```',
                        '',
                        '**Step 2: Install Dependencies**',
                        '```bash',
                        '# Install PHP dependencies',
                        'composer install',
                        '',
                        '# Install Node.js dependencies',
                        'npm install',
                        '```',
                        '',
                        '**Step 3: Environment Configuration**',
                        '```bash',
                        '# Copy environment file',
                        'cp .env.example .env',
                        '',
                        '# Generate application key',
                        'php artisan key:generate',
                        '```'
                    ]
                }
            ]
        },
        {
            id: 'payment-system',
            title: 'Payment System - The Heart of Fundraising',
            content: [
                'The payment system is the critical component that transforms good intentions into actual impact. Built on Stripe\'s robust infrastructure, it handles millions of dollars in donations while maintaining the highest standards of security and user experience.'
            ],
            subsections: [
                {
                    id: 'donation-journey',
                    title: 'The Donation Journey: From Click to Impact',
                    content: [
                        '**Step 1: Donation Initiation**',
                        'When a supporter decides to contribute, they\'re presented with carefully designed donation tiers that make giving both meaningful and accessible.',
                        '',
                        '**Step 2: Secure Token Generation**',
                        'Before any payment processing begins, the system generates a unique donation token using Laravel\'s `DonationToken` model.',
                        '',
                        '**Step 3: Payment Processing**',
                        'Using Stripe\'s advanced payment infrastructure, the system validates payment methods in real-time and processes payments with industry-leading security.',
                        '',
                        '**Step 4: Confirmation and Notification**',
                        'Upon successful payment, donors receive immediate confirmation, fundraisers are notified, and all statistics are updated in real-time.'
                    ]
                }
            ]
        },
        {
            id: 'database-structure',
            title: 'Database Structure - The Foundation of Data',
            content: [
                'The database architecture is carefully designed to support the complex relationships between fundraisers, donors, and transactions while maintaining data integrity, performance, and scalability.'
            ],
            subsections: [
                {
                    id: 'core-tables',
                    title: 'Core Entity Tables',
                    content: [
                        '**Users Table - The Fundraiser Profiles**',
                        'The users table serves as the central hub for all fundraiser information, storing both authentication data and fundraising-specific details.',
                        '',
                        '**Donors Table - The Contribution Records**',
                        'The donors table captures detailed information about each donation and donor, supporting both anonymous and public recognition preferences.',
                        '',
                        '**Messages Table - Communication Hub**',
                        'The messages table manages all contact form submissions and administrative communications.',
                        '',
                        '**Transactions Table - Financial Records**',
                        'The transactions table provides detailed tracking of all payment activities with comprehensive Stripe integration.'
                    ]
                }
            ]
        }
    ];

    generateTableOfContents();
    renderDocumentation();
    buildSearchIndex();
}

// Theme management
function initializeTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon();

    document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
}

function updateThemeIcon() {
    const icon = document.querySelector('#theme-toggle i');
    icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
}

// Navigation
function initializeNavigation() {
    // Mobile menu toggle
    document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
        document.getElementById('sidebar').classList.toggle('active');
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        const sidebar = document.getElementById('sidebar');
        const mobileToggle = document.getElementById('mobile-menu-toggle');

        if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
            sidebar.classList.remove('active');
        }
    });
}

// Search functionality
function initializeSearch() {
    const searchToggle = document.getElementById('search-toggle');
    const searchModal = document.getElementById('search-modal');
    const searchClose = document.getElementById('search-close');
    const searchInput = document.getElementById('search-input');

    searchToggle.addEventListener('click', () => {
        searchModal.classList.add('active');
        searchInput.focus();
    });

    searchClose.addEventListener('click', () => {
        searchModal.classList.remove('active');
    });

    searchModal.addEventListener('click', (e) => {
        if (e.target === searchModal) {
            searchModal.classList.remove('active');
        }
    });

    searchInput.addEventListener('input', performSearch);

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            searchModal.classList.add('active');
            searchInput.focus();
        }

        if (e.key === 'Escape') {
            searchModal.classList.remove('active');
        }
    });
}

function buildSearchIndex() {
    searchIndex = [];

    if (!documentationData) return;

    documentationData.forEach(section => {
        searchIndex.push({
            id: section.id,
            title: section.title,
            content: section.content.join(' '),
            type: 'section'
        });

        section.subsections.forEach(subsection => {
            searchIndex.push({
                id: subsection.id,
                title: subsection.title,
                content: subsection.content.join(' '),
                type: 'subsection',
                parent: section.title
            });
        });
    });
}

function performSearch() {
    const query = document.getElementById('search-input').value.toLowerCase();
    const resultsContainer = document.getElementById('search-results');

    if (query.length < 2) {
        resultsContainer.innerHTML = '';
        return;
    }

    const results = searchIndex.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query)
    ).slice(0, 10);

    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="search-no-results">No results found</div>';
        return;
    }

    const resultsHTML = results.map(result => `
        <div class="search-result" onclick="selectSearchResult('${result.id}')">
            <div class="search-result-title">${result.title}</div>
            ${result.parent ? `<div class="search-result-parent">${result.parent}</div>` : ''}
            <div class="search-result-content">${highlightQuery(result.content.substring(0, 150), query)}...</div>
        </div>
    `).join('');

    resultsContainer.innerHTML = resultsHTML;
}

function highlightQuery(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

function selectSearchResult(sectionId) {
    document.getElementById('search-modal').classList.remove('active');
    scrollToSection(sectionId);
}

// Scroll effects
function initializeScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        updateActiveNavItem();
        updateReadingProgress();
        updateBackToTopButton();
        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    });

    // Back to top button
    document.getElementById('back-to-top').addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

function updateActiveNavItem() {
    const sections = document.querySelectorAll('.doc-section, .subsection');
    const navLinks = document.querySelectorAll('.toc-link');

    let currentSection = '';

    sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section.id;
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === currentSection) {
            link.classList.add('active');
        }
    });
}

function updateReadingProgress() {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrollTop / docHeight) * 100;

    document.getElementById('reading-progress').style.width = `${Math.min(progress, 100)}%`;
}

function updateBackToTopButton() {
    const backToTop = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.classList.add('visible');
    } else {
        backToTop.classList.remove('visible');
    }
}

// Animations
function initializeAnimations() {
    // Animate hero stats
    animateCounters();

    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe sections for animation
    setTimeout(() => {
        document.querySelectorAll('.doc-section, .subsection').forEach(section => {
            observer.observe(section);
        });
    }, 2000);
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    });
}

// Utility functions
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const headerHeight = 70;
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - headerHeight - 20;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });

        // Close mobile menu if open
        document.getElementById('sidebar').classList.remove('active');
    }
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Enhanced features
function addCodeCopyButtons() {
    document.querySelectorAll('pre code').forEach((codeBlock) => {
        const pre = codeBlock.parentElement;
        const button = document.createElement('button');
        button.className = 'copy-code-btn';
        button.innerHTML = '<i class="fas fa-copy"></i>';
        button.title = 'Copy code';

        button.addEventListener('click', () => {
            navigator.clipboard.writeText(codeBlock.textContent).then(() => {
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.style.color = '#10b981';
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-copy"></i>';
                    button.style.color = '';
                }, 2000);
            });
        });

        pre.style.position = 'relative';
        pre.appendChild(button);
    });
}

function addSectionAnchors() {
    document.querySelectorAll('h2, h3, h4').forEach(heading => {
        if (heading.id) {
            const anchor = document.createElement('a');
            anchor.href = `#${heading.id}`;
            anchor.className = 'section-anchor';
            anchor.innerHTML = '<i class="fas fa-link"></i>';
            anchor.title = 'Link to this section';
            heading.appendChild(anchor);
        }
    });
}

function initializeTooltips() {
    // Add tooltips to various elements
    const tooltipElements = document.querySelectorAll('[title]');

    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('title');

    // Remove title to prevent default tooltip
    e.target.setAttribute('data-title', e.target.getAttribute('title'));
    e.target.removeAttribute('title');

    document.body.appendChild(tooltip);

    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

    setTimeout(() => tooltip.classList.add('visible'), 10);
}

function hideTooltip(e) {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }

    // Restore title
    if (e.target.getAttribute('data-title')) {
        e.target.setAttribute('title', e.target.getAttribute('data-title'));
        e.target.removeAttribute('data-title');
    }
}

function addPrintStyles() {
    const printStyles = `
        @media print {
            .header, .sidebar, .back-to-top, .search-modal, .floating-elements {
                display: none !important;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .doc-section {
                page-break-inside: avoid;
                margin-bottom: 2rem;
            }

            .section-header {
                page-break-after: avoid;
            }

            pre {
                page-break-inside: avoid;
                white-space: pre-wrap;
            }

            a {
                color: inherit !important;
                text-decoration: none !important;
            }

            .hero-section {
                background: none !important;
                color: black !important;
            }
        }
    `;

    const style = document.createElement('style');
    style.textContent = printStyles;
    document.head.appendChild(style);
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('search-modal').classList.add('active');
            document.getElementById('search-input').focus();
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            document.getElementById('search-modal').classList.remove('active');
            document.getElementById('sidebar').classList.remove('active');
        }

        // Arrow keys for navigation
        if (e.altKey) {
            const sections = Array.from(document.querySelectorAll('.doc-section'));
            const currentSection = sections.find(section => {
                const rect = section.getBoundingClientRect();
                return rect.top <= 100 && rect.bottom >= 100;
            });

            if (currentSection) {
                const currentIndex = sections.indexOf(currentSection);

                if (e.key === 'ArrowUp' && currentIndex > 0) {
                    e.preventDefault();
                    scrollToSection(sections[currentIndex - 1].id);
                } else if (e.key === 'ArrowDown' && currentIndex < sections.length - 1) {
                    e.preventDefault();
                    scrollToSection(sections[currentIndex + 1].id);
                }
            }
        }
    });
}

function addTableOfContentsCollapse() {
    document.querySelectorAll('.toc ul ul').forEach(sublist => {
        const parentLi = sublist.parentElement;
        const parentLink = parentLi.querySelector('a');

        if (parentLink) {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'toc-toggle';
            toggleBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
            toggleBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                sublist.classList.toggle('collapsed');
                toggleBtn.classList.toggle('rotated');
            });

            parentLink.appendChild(toggleBtn);
        }
    });
}

function initializeProgressiveEnhancement() {
    // Add enhanced features after initial load
    setTimeout(() => {
        addCodeCopyButtons();
        addSectionAnchors();
        initializeTooltips();
        addPrintStyles();
        initializeKeyboardShortcuts();
        addTableOfContentsCollapse();

        // Add smooth reveal animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.doc-section, .subsection').forEach(section => {
            observer.observe(section);
        });
    }, 1000);
}

// Enhanced scroll behavior
function smoothScrollToElement(element, offset = 90) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
    });
}

// URL hash handling
function handleHashChange() {
    const hash = window.location.hash.substring(1);
    if (hash) {
        const element = document.getElementById(hash);
        if (element) {
            setTimeout(() => {
                smoothScrollToElement(element);
            }, 100);
        }
    }
}

// Initialize hash handling
window.addEventListener('hashchange', handleHashChange);
window.addEventListener('load', handleHashChange);

// Update initialization
async function initializeApp() {
    showLoadingScreen();

    await loadDocumentationData();

    initializeTheme();
    initializeNavigation();
    initializeSearch();
    initializeScrollEffects();
    initializeAnimations();
    initializeProgressiveEnhancement();

    hideLoadingScreen();

    // Handle initial hash
    handleHashChange();
}

// Make scrollToSection globally available
window.scrollToSection = scrollToSection;
