// Global variables
let documentationData = null;
let currentTheme = localStorage.getItem('theme') || 'light';
let searchIndex = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    // Show loading screen
    showLoadingScreen();

    // Load documentation data
    await loadDocumentationData();

    // Initialize components
    initializeTheme();
    initializeNavigation();
    initializeSearch();
    initializeScrollEffects();
    initializeAnimations();

    // Hide loading screen
    hideLoadingScreen();
}

function showLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    loadingScreen.style.display = 'flex';
}

function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }, 1500);
}

// Load and parse documentation data
async function loadDocumentationData() {
    try {
        const response = await fetch('PROJECT_DOCUMENTATION.md');
        const markdown = await response.text();
        documentationData = parseMarkdown(markdown);
        generateTableOfContents();
        renderDocumentation();
        buildSearchIndex();
    } catch (error) {
        console.error('Error loading documentation:', error);
        // Fallback to static content if markdown file is not available
        loadFallbackContent();
    }
}

function parseMarkdown(markdown) {
    const lines = markdown.split('\n');
    const sections = [];
    let currentSection = null;
    let currentSubsection = null;

    lines.forEach(line => {
        const trimmedLine = line.trim();

        // Main headers (##)
        if (trimmedLine.startsWith('## ')) {
            if (currentSection) {
                sections.push(currentSection);
            }
            currentSection = {
                id: generateId(trimmedLine.substring(3)),
                title: trimmedLine.substring(3),
                content: [],
                subsections: []
            };
            currentSubsection = null;
        }
        // Sub headers (###)
        else if (trimmedLine.startsWith('### ')) {
            if (currentSection) {
                currentSubsection = {
                    id: generateId(trimmedLine.substring(4)),
                    title: trimmedLine.substring(4),
                    content: []
                };
                currentSection.subsections.push(currentSubsection);
            }
        }
        // Content
        else if (trimmedLine) {
            const target = currentSubsection || currentSection;
            if (target) {
                target.content.push(line);
            }
        }
    });

    if (currentSection) {
        sections.push(currentSection);
    }

    return sections;
}

function generateId(title) {
    return title.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

function generateTableOfContents() {
    const tocElement = document.getElementById('toc');
    if (!documentationData) return;

    const tocHTML = documentationData.map(section => {
        const subsectionsHTML = section.subsections.map(subsection =>
            `<li><a href="#${subsection.id}" class="toc-link" data-section="${subsection.id}">${subsection.title}</a></li>`
        ).join('');

        return `
            <li>
                <a href="#${section.id}" class="toc-link" data-section="${section.id}">${section.title}</a>
                ${subsectionsHTML ? `<ul>${subsectionsHTML}</ul>` : ''}
            </li>
        `;
    }).join('');

    tocElement.innerHTML = `<ul>${tocHTML}</ul>`;

    // Add click handlers
    document.querySelectorAll('.toc-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            scrollToSection(sectionId);
        });
    });
}

function renderDocumentation() {
    const contentElement = document.getElementById('documentation-content');
    if (!documentationData) return;

    const contentHTML = documentationData.map(section => {
        const subsectionsHTML = section.subsections.map(subsection => `
            <div class="subsection" id="${subsection.id}">
                <h3>${subsection.title}</h3>
                <div class="subsection-content">
                    ${formatContent(subsection.content)}
                </div>
            </div>
        `).join('');

        return `
            <section class="doc-section" id="${section.id}">
                <div class="section-header">
                    <h2>${section.title}</h2>
                    <div class="section-actions">
                        <button class="copy-link" data-section="${section.id}" title="Copy link">
                            <i class="fas fa-link"></i>
                        </button>
                    </div>
                </div>
                <div class="section-content">
                    ${formatContent(section.content)}
                    ${subsectionsHTML}
                </div>
            </section>
        `;
    }).join('');

    contentElement.innerHTML = contentHTML;

    // Add copy link functionality
    document.querySelectorAll('.copy-link').forEach(button => {
        button.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section');
            const url = `${window.location.origin}${window.location.pathname}#${sectionId}`;
            navigator.clipboard.writeText(url).then(() => {
                showNotification('Link copied to clipboard!');
            });
        });
    });
}

function formatContent(contentLines) {
    let html = '';
    let inCodeBlock = false;
    let inList = false;
    let listType = '';

    contentLines.forEach(line => {
        const trimmedLine = line.trim();

        // Code blocks
        if (trimmedLine.startsWith('```')) {
            if (inCodeBlock) {
                html += '</code></pre>';
                inCodeBlock = false;
            } else {
                const language = trimmedLine.substring(3) || 'text';
                html += `<pre><code class="language-${language}">`;
                inCodeBlock = true;
            }
            return;
        }

        if (inCodeBlock) {
            html += line + '\n';
            return;
        }

        // Lists
        if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ')) {
            if (!inList) {
                html += '<ul>';
                inList = true;
                listType = 'ul';
            } else if (listType === 'ol') {
                html += '</ol><ul>';
                listType = 'ul';
            }
            html += `<li>${trimmedLine.substring(2)}</li>`;
            return;
        }

        if (/^\d+\.\s/.test(trimmedLine)) {
            if (!inList) {
                html += '<ol>';
                inList = true;
                listType = 'ol';
            } else if (listType === 'ul') {
                html += '</ul><ol>';
                listType = 'ol';
            }
            html += `<li>${trimmedLine.replace(/^\d+\.\s/, '')}</li>`;
            return;
        }

        // Close lists if not continuing
        if (inList && !trimmedLine.startsWith('- ') && !trimmedLine.startsWith('* ') && !/^\d+\.\s/.test(trimmedLine)) {
            html += listType === 'ul' ? '</ul>' : '</ol>';
            inList = false;
            listType = '';
        }

        // Headers
        if (trimmedLine.startsWith('####')) {
            html += `<h4>${trimmedLine.substring(5)}</h4>`;
        } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
            html += `<p><strong>${trimmedLine.slice(2, -2)}</strong></p>`;
        } else if (trimmedLine) {
            html += `<p>${formatInlineElements(trimmedLine)}</p>`;
        }
    });

    // Close any open lists
    if (inList) {
        html += listType === 'ul' ? '</ul>' : '</ol>';
    }

    return html;
}

function formatInlineElements(text) {
    // Bold text
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic text
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Inline code
    text = text.replace(/`(.*?)`/g, '<code>$1</code>');

    // Links
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

    return text;
}

function loadFallbackContent() {
    // Fallback content structure
    documentationData = [
        {
            id: 'project-overview',
            title: 'Project Overview',
            content: ['ASFL Gauntlet is a comprehensive fundraising platform built with Laravel 11, designed to support cancer research through a 24-hour basketball marathon event.'],
            subsections: []
        },
        {
            id: 'installation-guide',
            title: 'Installation Guide',
            content: ['Follow these steps to set up the ASFL Gauntlet platform on your local development environment.'],
            subsections: []
        }
    ];

    generateTableOfContents();
    renderDocumentation();
}

// Theme management
function initializeTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon();

    document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
}

function updateThemeIcon() {
    const icon = document.querySelector('#theme-toggle i');
    icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
}

// Navigation
function initializeNavigation() {
    // Mobile menu toggle
    document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
        document.getElementById('sidebar').classList.toggle('active');
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        const sidebar = document.getElementById('sidebar');
        const mobileToggle = document.getElementById('mobile-menu-toggle');

        if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
            sidebar.classList.remove('active');
        }
    });
}

// Search functionality
function initializeSearch() {
    const searchToggle = document.getElementById('search-toggle');
    const searchModal = document.getElementById('search-modal');
    const searchClose = document.getElementById('search-close');
    const searchInput = document.getElementById('search-input');

    searchToggle.addEventListener('click', () => {
        searchModal.classList.add('active');
        searchInput.focus();
    });

    searchClose.addEventListener('click', () => {
        searchModal.classList.remove('active');
    });

    searchModal.addEventListener('click', (e) => {
        if (e.target === searchModal) {
            searchModal.classList.remove('active');
        }
    });

    searchInput.addEventListener('input', performSearch);

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            searchModal.classList.add('active');
            searchInput.focus();
        }

        if (e.key === 'Escape') {
            searchModal.classList.remove('active');
        }
    });
}

function buildSearchIndex() {
    searchIndex = [];

    if (!documentationData) return;

    documentationData.forEach(section => {
        searchIndex.push({
            id: section.id,
            title: section.title,
            content: section.content.join(' '),
            type: 'section'
        });

        section.subsections.forEach(subsection => {
            searchIndex.push({
                id: subsection.id,
                title: subsection.title,
                content: subsection.content.join(' '),
                type: 'subsection',
                parent: section.title
            });
        });
    });
}

function performSearch() {
    const query = document.getElementById('search-input').value.toLowerCase();
    const resultsContainer = document.getElementById('search-results');

    if (query.length < 2) {
        resultsContainer.innerHTML = '';
        return;
    }

    const results = searchIndex.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query)
    ).slice(0, 10);

    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="search-no-results">No results found</div>';
        return;
    }

    const resultsHTML = results.map(result => `
        <div class="search-result" onclick="selectSearchResult('${result.id}')">
            <div class="search-result-title">${result.title}</div>
            ${result.parent ? `<div class="search-result-parent">${result.parent}</div>` : ''}
            <div class="search-result-content">${highlightQuery(result.content.substring(0, 150), query)}...</div>
        </div>
    `).join('');

    resultsContainer.innerHTML = resultsHTML;
}

function highlightQuery(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

function selectSearchResult(sectionId) {
    document.getElementById('search-modal').classList.remove('active');
    scrollToSection(sectionId);
}

// Scroll effects
function initializeScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        updateActiveNavItem();
        updateReadingProgress();
        updateBackToTopButton();
        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    });

    // Back to top button
    document.getElementById('back-to-top').addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

function updateActiveNavItem() {
    const sections = document.querySelectorAll('.doc-section, .subsection');
    const navLinks = document.querySelectorAll('.toc-link');

    let currentSection = '';

    sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section.id;
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === currentSection) {
            link.classList.add('active');
        }
    });
}

function updateReadingProgress() {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrollTop / docHeight) * 100;

    document.getElementById('reading-progress').style.width = `${Math.min(progress, 100)}%`;
}

function updateBackToTopButton() {
    const backToTop = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.classList.add('visible');
    } else {
        backToTop.classList.remove('visible');
    }
}

// Animations
function initializeAnimations() {
    // Animate hero stats
    animateCounters();

    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe sections for animation
    setTimeout(() => {
        document.querySelectorAll('.doc-section, .subsection').forEach(section => {
            observer.observe(section);
        });
    }, 2000);
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    });
}

// Utility functions
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const headerHeight = 70;
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - headerHeight - 20;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });

        // Close mobile menu if open
        document.getElementById('sidebar').classList.remove('active');
    }
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Enhanced features
function addCodeCopyButtons() {
    document.querySelectorAll('pre code').forEach((codeBlock, index) => {
        const pre = codeBlock.parentElement;
        const button = document.createElement('button');
        button.className = 'copy-code-btn';
        button.innerHTML = '<i class="fas fa-copy"></i>';
        button.title = 'Copy code';

        button.addEventListener('click', () => {
            navigator.clipboard.writeText(codeBlock.textContent).then(() => {
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.style.color = '#10b981';
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-copy"></i>';
                    button.style.color = '';
                }, 2000);
            });
        });

        pre.style.position = 'relative';
        pre.appendChild(button);
    });
}

function addSectionAnchors() {
    document.querySelectorAll('h2, h3, h4').forEach(heading => {
        if (heading.id) {
            const anchor = document.createElement('a');
            anchor.href = `#${heading.id}`;
            anchor.className = 'section-anchor';
            anchor.innerHTML = '<i class="fas fa-link"></i>';
            anchor.title = 'Link to this section';
            heading.appendChild(anchor);
        }
    });
}

function initializeTooltips() {
    // Add tooltips to various elements
    const tooltipElements = document.querySelectorAll('[title]');

    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('title');

    // Remove title to prevent default tooltip
    e.target.setAttribute('data-title', e.target.getAttribute('title'));
    e.target.removeAttribute('title');

    document.body.appendChild(tooltip);

    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

    setTimeout(() => tooltip.classList.add('visible'), 10);
}

function hideTooltip(e) {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }

    // Restore title
    if (e.target.getAttribute('data-title')) {
        e.target.setAttribute('title', e.target.getAttribute('data-title'));
        e.target.removeAttribute('data-title');
    }
}

function addPrintStyles() {
    const printStyles = `
        @media print {
            .header, .sidebar, .back-to-top, .search-modal, .floating-elements {
                display: none !important;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .doc-section {
                page-break-inside: avoid;
                margin-bottom: 2rem;
            }

            .section-header {
                page-break-after: avoid;
            }

            pre {
                page-break-inside: avoid;
                white-space: pre-wrap;
            }

            a {
                color: inherit !important;
                text-decoration: none !important;
            }

            .hero-section {
                background: none !important;
                color: black !important;
            }
        }
    `;

    const style = document.createElement('style');
    style.textContent = printStyles;
    document.head.appendChild(style);
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('search-modal').classList.add('active');
            document.getElementById('search-input').focus();
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            document.getElementById('search-modal').classList.remove('active');
            document.getElementById('sidebar').classList.remove('active');
        }

        // Arrow keys for navigation
        if (e.altKey) {
            const sections = Array.from(document.querySelectorAll('.doc-section'));
            const currentSection = sections.find(section => {
                const rect = section.getBoundingClientRect();
                return rect.top <= 100 && rect.bottom >= 100;
            });

            if (currentSection) {
                const currentIndex = sections.indexOf(currentSection);

                if (e.key === 'ArrowUp' && currentIndex > 0) {
                    e.preventDefault();
                    scrollToSection(sections[currentIndex - 1].id);
                } else if (e.key === 'ArrowDown' && currentIndex < sections.length - 1) {
                    e.preventDefault();
                    scrollToSection(sections[currentIndex + 1].id);
                }
            }
        }
    });
}

function addTableOfContentsCollapse() {
    document.querySelectorAll('.toc ul ul').forEach(sublist => {
        const parentLi = sublist.parentElement;
        const parentLink = parentLi.querySelector('a');

        if (parentLink) {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'toc-toggle';
            toggleBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
            toggleBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                sublist.classList.toggle('collapsed');
                toggleBtn.classList.toggle('rotated');
            });

            parentLink.appendChild(toggleBtn);
        }
    });
}

function initializeProgressiveEnhancement() {
    // Add enhanced features after initial load
    setTimeout(() => {
        addCodeCopyButtons();
        addSectionAnchors();
        initializeTooltips();
        addPrintStyles();
        initializeKeyboardShortcuts();
        addTableOfContentsCollapse();

        // Add smooth reveal animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.doc-section, .subsection').forEach(section => {
            observer.observe(section);
        });
    }, 1000);
}

// Enhanced scroll behavior
function smoothScrollToElement(element, offset = 90) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
    });
}

// URL hash handling
function handleHashChange() {
    const hash = window.location.hash.substring(1);
    if (hash) {
        const element = document.getElementById(hash);
        if (element) {
            setTimeout(() => {
                smoothScrollToElement(element);
            }, 100);
        }
    }
}

// Initialize hash handling
window.addEventListener('hashchange', handleHashChange);
window.addEventListener('load', handleHashChange);

// Update initialization
async function initializeApp() {
    showLoadingScreen();

    await loadDocumentationData();

    initializeTheme();
    initializeNavigation();
    initializeSearch();
    initializeScrollEffects();
    initializeAnimations();
    initializeProgressiveEnhancement();

    hideLoadingScreen();

    // Handle initial hash
    handleHashChange();
}

// Make scrollToSection globally available
window.scrollToSection = scrollToSection;
