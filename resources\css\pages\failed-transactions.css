/* Failed Transactions Page Styles */

/* Ensure page content is visible */
.container-fluid {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 2rem 0;
}

section {
    background: transparent;
}

.dashboard-header {
    background: linear-gradient(135deg, #19345e 0%, #f05522 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card.premium {
    background: linear-gradient(135deg, #19345e 0%, #2a4a75 100%);
    color: white;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.trend-indicator {
    background: rgba(240, 85, 34, 0.1);
    color: #f05522;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.trend-text {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-left: 0.5rem;
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.3;
}

.chart-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    min-height: 400px;
    margin-bottom: 2rem;
}

.card-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-title {
    margin: 0;
    font-weight: 600;
    color: #19345e;
}

.chart-container {
    padding: 1.5rem;
    height: 300px;
}

/* Table container styles */
#table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    min-height: 200px;
}

/* Ensure table visibility */
.user-table {
    width: 100%;
    background: white;
}

.user-table tbody tr {
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.user-table tbody tr:hover {
    background-color: rgba(240, 85, 34, 0.03);
}

.table-responsive {
    overflow-x: auto;
}

.search-filter-container {
    background: linear-gradient(145deg, #f7f9fc, #ffffff);
    border: 1px solid #19345e;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.data-table {
    margin: 0;
    width: 100%;
    border-collapse: collapse;
}

.data-table td,
.data-table th {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.data-table tbody tr {
    background-color: white;
}

.data-table thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #19345e;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.data-table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.3s ease;
}

.data-table tbody tr:hover {
    background-color: rgba(240, 85, 34, 0.03);
}

.donor-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    margin-right: 0.75rem;
}

.donor-avatar.primary {
    background: linear-gradient(135deg, #19345e, #f05522);
}

.donor-name {
    font-weight: 500;
    color: #19345e;
}

.export-btn {
    background: linear-gradient(135deg, #f05522, #ff6b3d);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(240, 85, 34, 0.3);
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(240, 85, 34, 0.4);
    color: white;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

.status-retry_pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-retry_processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-resolved {
    background-color: #d4edda;
    color: #155724;
}

.status-abandoned {
    background-color: #e2e3e5;
    color: #383d41;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    margin: 0.125rem;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: scale(1.05);
}

/* Animation classes */
.animated {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

.animated.delay-1 {
    transition-delay: 0.1s;
}
.animated.delay-2 {
    transition-delay: 0.2s;
}
.animated.delay-3 {
    transition-delay: 0.3s;
}
.animated.delay-4 {
    transition-delay: 0.4s;
}

/* Pagination styles */
.pagination .page-item.disabled {
    cursor: not-allowed;
}

.pagination .page-item.disabled .page-link {
    cursor: not-allowed;
    pointer-events: none;
}

.pagination .page-link {
    color: #19345e;
    border: 1px solid rgba(25, 52, 94, 0.2);
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #19345e;
    color: white;
    border-color: #19345e;
}

.pagination .page-item.active .page-link {
    background-color: #19345e;
    border-color: #19345e;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .search-filter-container {
        border-radius: 15px;
    }

    .chart-container {
        height: 250px;
    }
}
