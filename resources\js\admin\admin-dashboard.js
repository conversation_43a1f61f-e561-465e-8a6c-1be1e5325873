import { createSlider } from "../appTwo.js";
document.addEventListener("DOMContentLoaded", function () {
    tinymce.init({
        selector: "textarea#message",
        width: 470,
        height: 300,
        plugins: [
            "advlist",
            "autolink",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "wordcount",
            "code",
            "fullscreen",
            "insertdatetime",
            "table",
            "print",
        ],
        toolbar:
            "undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen",
        menubar: false,
        content_style:
            "body { font-family: Helvetica, Arial, sans-serif; font-size: 16px; }",
    });

    let debounceTimer;

    function fetchUsers(
        url = "/admin/get-all-users",
        search = "",
        sortBy = ""
    ) {
        const userContainer = document.getElementById("table-body");
        const paginationLinks = document.getElementById("pagination-links");
        const cardContainer = document.querySelector("#user-container .card");

        cardContainer.innerHTML = `
    <div class="loader-container">
        <div class="spinner-border" role="status" style="color: #19345E; width: 3rem; height: 3rem;"></div>
        <p class="mt-3 text-muted">Loading fundraisers...</p>
    </div>
`;
        paginationLinks.innerHTML = "";

        const fullUrl = new URL(url, window.location.origin);
        fullUrl.searchParams.set("search", search);

        if (sortBy) {
            fullUrl.searchParams.set("sortBy", sortBy);
        }

        fetch(fullUrl)
            .then((response) => response.json())
            .then((data) => {
                cardContainer.innerHTML = `
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="bg-light">
                            <tr>
                                <th class="py-3 px-4 fw-bold" style="color: #19345E;">Name</th>
                                <th class="py-3 px-4 fw-bold" style="color: #19345E;">City</th>
                                <th class="py-3 px-4 fw-bold text-center" style="color: #19345E;">Total Collected</th>
                                <th class="py-3 px-4 fw-bold text-center" style="color: #19345E;">
                                    Actions

                                </th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                        </tbody>
                    </table>
                </div>
            </div>
        `;

                const tableBody = document.getElementById("table-body");
                paginationLinks.innerHTML = "";

                if (data.data.length === 0) {
                    cardContainer.innerHTML = `
              <div class="empty-state-container text-center">
                    <i class="fas fa-hand-holding-heart fa-5x mb-3" style="color: #19345E; opacity: 0.7;"></i>
                    <h3 style="color: #19345E;">No fundraisers found</h3>
                    <p class="text-muted">Try adjusting your search criteria</p>
                </div>

            `;
                    return;
                }

                data.data.forEach((user) => {
                    const row = document.createElement("tr");

                    row.innerHTML = `
                <td class="py-3 px-4">
                    <div class="d-flex align-items-center">
                        <div class="avatar-container me-3 rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                             <img src="${
                                 user.profile_photo ||
                                 "default_profile_photo_url"
                             }" alt="${
                        user.name
                    }" class="rounded-circle" style="width: 40px; height: 40px;">
                        </div>
                        <div>
                            <div class="fw-bold">${user.name}</div>
                            <div class="text-muted small">${
                                user.email || "No email"
                            }</div>
                        </div>
                    </div>
                </td>
                <td class="py-3 px-4">${user.city || "N/A"}</td>
                <td class="py-3 px-4 text-center">
                    <span class="fundraiser-amount">$${(
                        user.total_collected || 0
                    ).toLocaleString()}</span>
                </td>
                <td class="py-3 px-4 text-center">
                    <a href="${
                        user.donationPageUrl
                    }" class="action-btn view-btn" title="View">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="javascript:void(0)" class="action-btn edit-btn" data-user-id="${
                        user.id
                    }" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                    <button class="action-btn delete-btn" title="Delete" data-id="${
                        user.id
                    }">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;

                    tableBody.appendChild(row);
                });

                if (data.total > data.per_page && data.last_page > 1) {
                    let firstPage = document.createElement("li");
                    firstPage.className = "page-item";
                    firstPage.innerHTML = `<a class="page-link" href="#"><i class="fas fa-angle-double-left"></i></a>`;
                    firstPage.addEventListener("click", function (e) {
                        e.preventDefault();
                        fetchUsers(
                            data.first_page_url,
                            document.getElementById("search").value.trim(),
                            document.getElementById("sort-filter").value
                        );
                    });
                    paginationLinks.appendChild(firstPage);

                    data.links.forEach((link, index) => {
                        if (
                            !link.url ||
                            index === 0 ||
                            index === data.links.length - 1
                        )
                            return;

                        let pageLink = document.createElement("li");
                        pageLink.className = `page-item ${
                            link.active ? "active" : ""
                        }`;
                        pageLink.innerHTML = `<a class="page-link" href="#">${link.label.replace(
                            /<.*?>/g,
                            ""
                        )}</a>`;

                        pageLink.addEventListener("click", function (e) {
                            e.preventDefault();
                            fetchUsers(
                                link.url,
                                document.getElementById("search").value.trim(),
                                document.getElementById("sort-filter").value
                            );
                        });

                        paginationLinks.appendChild(pageLink);
                    });

                    let lastPage = document.createElement("li");
                    lastPage.className = "page-item";
                    lastPage.innerHTML = `<a class="page-link" href="#"><i class="fas fa-angle-double-right"></i></a>`;
                    lastPage.addEventListener("click", function (e) {
                        e.preventDefault();
                        fetchUsers(
                            data.last_page_url,
                            document.getElementById("search").value.trim(),
                            document.getElementById("sort-filter").value
                        );
                    });
                    paginationLinks.appendChild(lastPage);
                }
            })
            .catch((error) => {
                console.error("Error fetching data:", error);
                cardContainer.innerHTML = `
            <div class="text-center p-5">
                <div class="mb-3"><i class="fas fa-exclamation-triangle fa-3x text-warning"></i></div>
                <h4 class="text-danger">Something went wrong</h4>
                <p class="text-muted">Please try again later or contact support</p>
                <button class="btn gradient-btn mt-3" onclick="fetchUsers()">
                    <i class="fas fa-sync-alt me-2"></i> Try Again
                </button>
            </div>
        `;
            });
    }

    let selectedUserId = null;
    document.addEventListener("click", function (e) {
        if (e.target.closest(".delete-btn")) {
            const btn = e.target.closest(".delete-btn");
            selectedUserId = btn.getAttribute("data-id");

            const confirmModal = new bootstrap.Modal(
                document.getElementById("confirmDeleteModal")
            );
            confirmModal.show();
        }
    });

    document
        .getElementById("confirmDeleteBtn")
        .addEventListener("click", function handleConfirmDelete() {
            if (!selectedUserId) return;

            const spinner = this.querySelector(".fa-spinner");
            spinner.classList.remove("d-none");

            const url = route("admin.deleteUser");

            fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
                body: JSON.stringify({ id: selectedUserId }),
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success === true) {
                        setTimeout(() => {
                            spinner.classList.add("d-none");

                            const confirmModal = bootstrap.Modal.getInstance(
                                document.getElementById("confirmDeleteModal")
                            );
                            confirmModal.hide();

                            const row = document
                                .querySelector(`[data-id="${selectedUserId}"]`)
                                ?.closest("tr");
                            if (row) row.remove();
                            createSlider(
                                "success",
                                "User Deleted Successfully",
                                { title: "Success" }
                            );

                            selectedUserId = null;
                        }, 1000);
                    } else {
                        spinner.classList.add("d-none");
                        alert("Something went wrong!");
                    }
                })
                .catch((error) => {
                    console.error("Delete error:", error);
                    spinner.classList.add("d-none");
                    alert("Failed to delete user.");
                });
        });

    document.getElementById("search").addEventListener("input", function () {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            fetchUsers(
                "/admin/get-all-users",
                this.value.trim(),
                document.getElementById("sort-filter").value
            );
        }, 300);
    });

    document
        .getElementById("sort-filter")
        .addEventListener("change", function () {
            fetchUsers(
                "/admin/get-all-users",
                document.getElementById("search").value.trim(),
                this.value
            );
        });

    fetchUsers();

    // Export Fundraisers CSV functionality
    const exportFundraisersBtn = document.getElementById('exportFundraisersBtn');
    if (exportFundraisersBtn) {
        exportFundraisersBtn.addEventListener('click', function() {
            const exportText = this.querySelector('.export-text');
            const exportLoader = this.querySelector('.export-loader');
            
            // Show loader
            exportText.classList.add('d-none');
            exportLoader.classList.remove('d-none');
            this.disabled = true;
            
            // Create a temporary link to trigger download
            const link = document.createElement('a');
            link.href = '/admin/export-fundraisers';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Hide loader and show success message
            setTimeout(() => {
                exportText.classList.remove('d-none');
                exportLoader.classList.add('d-none');
                this.disabled = false;
                
                // Show success notification
                createSlider('success', 'Fundraisers CSV file downloaded successfully!', {
                    title: 'Export Complete'
                });
            }, 1500);
        });
    }

    //Edit goal

    let editGoalForm = document.getElementById("editGoalForm");
    let initialFormData = new FormData(editGoalForm);
    let initialFormObject = Object.fromEntries(initialFormData.entries());

    let saveChangesButton = document.getElementById("saveChangesButton");

    editGoalForm.addEventListener("submit", function (e) {
        e.preventDefault();

        let currentFormData = new FormData(editGoalForm);
        let currentFormObject = Object.fromEntries(currentFormData.entries());

        if (
            JSON.stringify(initialFormObject) ===
            JSON.stringify(currentFormObject)
        ) {
            return;
        }

        saveChangesButton.disabled = true;
        saveChangesButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...`;

        let url = route("admin.editCurrrentYearGoal");

        fetch(url, {
            method: editGoalForm.method,
            headers: {
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: currentFormData,
        })
            .then((response) => response.json())
            .then((data) => {
                saveChangesButton.innerHTML = `<i class="fas fa-check-circle text-white"></i> Saved`;
                setTimeout(() => {
                    saveChangesButton.innerHTML = `Save Changes`;
                    saveChangesButton.disabled = false;
                }, 2000);
            })
            .catch((error) => {
                saveChangesButton.innerHTML = `Save Changes`;
                saveChangesButton.disabled = false;
            });
    });

    //add user Account

    const togglePassword = document.querySelector(".toggle-password");
    if (togglePassword) {
        togglePassword.addEventListener("click", function () {
            const passwordInput = document.querySelector("#password");
            const type =
                passwordInput.getAttribute("type") === "password"
                    ? "text"
                    : "password";
            passwordInput.setAttribute("type", type);

            this.classList.toggle("fa-eye");
            this.classList.toggle("fa-eye-slash");
        });
    }

    const togglePasswordBtn = document.querySelector(".toggle-password-btn");
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener("click", function () {
            const passwordInput = document.querySelector("#adminPassword");
            const type =
                passwordInput.getAttribute("type") === "password"
                    ? "text"
                    : "password";
            passwordInput.setAttribute("type", type);

            const icon = this.querySelector("i");
            icon.classList.toggle("fa-eye");
            icon.classList.toggle("fa-eye-slash");
        });
    }

    //image preview
    const fileInput = document.querySelector("#fileUpload");
    const uploadPrompt = document.querySelector("#upload-prompt");
    const previewContainer = document.querySelector("#preview-container");
    const previewImage = document.querySelector("#image-preview");
    const removeButton = document.querySelector("#remove-image");
    let fileToUpload;

    fileInput.addEventListener("change", function () {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();

            reader.addEventListener("load", function () {
                previewImage.setAttribute("src", this.result);
                uploadPrompt.style.display = "none";
                previewContainer.style.display = "block";
            });

            reader.readAsDataURL(file);
        }
    });

    if (removeButton) {
        removeButton.addEventListener("click", function (e) {
            e.preventDefault();
            e.stopPropagation();
            fileInput.value = "";
            previewContainer.style.display = "none";
            uploadPrompt.style.display = "block";
        });
    }

    ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
        document
            .querySelector(".file-upload-container")
            .addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    const uploadContainer = document.querySelector(".file-upload-container");
    if (uploadContainer) {
        ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) =>
            uploadContainer.addEventListener(eventName, preventDefaults, false)
        );

        ["dragenter", "dragover"].forEach((eventName) =>
            uploadContainer.addEventListener(
                eventName,
                () => uploadContainer.classList.add("bg-light"),
                false
            )
        );

        ["dragleave", "drop"].forEach((eventName) =>
            uploadContainer.addEventListener(
                eventName,
                () => uploadContainer.classList.remove("bg-light"),
                false
            )
        );

        uploadContainer.addEventListener("drop", handleDrop, false);
    }
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const file = e.dataTransfer.files[0];

        if (file) {
            fileToUpload = file;
            console.log("File name:", file.name);
        }

        if (file && file.type.match("image.*")) {
            fileInput.files = dt.files;

            const reader = new FileReader();
            reader.addEventListener("load", function () {
                previewImage.setAttribute("src", this.result);
                uploadPrompt.style.display = "none";
                previewContainer.style.display = "block";
            });

            reader.readAsDataURL(file);
        }
    }

    const userForm = document.getElementById("userRegistrationForm");
    if (userForm) {
        userForm.addEventListener("submit", async function (event) {
            event.preventDefault();

            const formData = new FormData(userForm);
            if (!validateForm(formData)) return;

            if (fileToUpload) {
                formData.append("file", fileToUpload);
            }

            const button = document.getElementById("addUserAccount");
            const url = route("admin.addUserAccount");

            await submitFormData(
                url,
                formData,
                button,
                "User account created successfully",
                "Creating user...",
                "addUserModal"
            );
        });
    }

    //edit User Account=>
    document.addEventListener("click", async function (e) {
        const button = e.target.closest(".edit-btn");
        if (!button) return;

        const userId = button.getAttribute("data-user-id");
        const modalElement = document.getElementById("editUserModal");
        const container = document.getElementById("editUserFormContainer");
        const modal = new bootstrap.Modal(modalElement);

        modal.show();

        container.innerHTML = `
            <div class="text-center my-5">
                <div class="spinner-border text-primary" role="status"></div>
            </div>
        `;

        try {
            const response = await fetch(`/admin/users/${userId}/edit`);
            const html = await response.text();

            container.innerHTML = html;

            if (tinymce.get("messageForDonors")) {
                tinymce.get("messageForDonors").remove();
            }
            const editorWidth =
                window.innerWidth < 576
                    ? 300
                    : window.innerWidth < 768
                    ? 500
                    : 700;

            tinymce.init({
                selector: "#messageForDonors",
                width: editorWidth,
                height: 300,
                plugins: [
                    "advlist",
                    "autolink",
                    "link",
                    "image",
                    "charmap",
                    "preview",
                    "anchor",
                    "searchreplace",
                    "wordcount",
                    "code",
                    "fullscreen",
                    "insertdatetime",
                    "table",
                    "print",
                ],
                toolbar:
                    "undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen",
                menubar: false,
                content_style:
                    "body { font-family: Helvetica, Arial, sans-serif; font-size: 16px; }",
            });

            const fileInput = container.querySelector("#userPictureInput");
            const previewImg = container.querySelector("#userProfilePreview");
            if (fileInput && previewImg) {
                fileInput.addEventListener("change", function (e) {
                    if (e.target.files.length > 0) {
                        // Initialize cropping functionality
                        initializeAdminCropper(
                            e.target.files[0],
                            previewImg,
                            fileInput
                        );
                    }
                });
            }

            // Add crop button functionality for existing profile picture
            const cropButton = container.querySelector("#cropProfileBtn");
            if (cropButton) {
                cropButton.addEventListener("click", function () {
                    // Get the current profile image source
                    const currentImageSrc = previewImg.src;
                    if (
                        currentImageSrc &&
                        !currentImageSrc.includes("default")
                    ) {
                        // Create a temporary file from the current image
                        fetch(currentImageSrc)
                            .then((res) => res.blob())
                            .then((blob) => {
                                const file = new File(
                                    [blob],
                                    "current_profile.jpg",
                                    { type: "image/jpeg" }
                                );
                                initializeAdminCropper(
                                    file,
                                    previewImg,
                                    fileInput
                                );
                            });
                    }
                });
            }

            const form = container.querySelector("form");

            form.addEventListener("submit", async function (event) {
                event.preventDefault();

                let formData = new FormData(form);

                const submitBtn = document.getElementById("saveUserChanges");

                const originalBtnText = submitBtn.innerHTML;

                submitBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...`;
                submitBtn.disabled = true;

                form.querySelectorAll(".is-invalid").forEach((el) =>
                    el.classList.remove("is-invalid")
                );
                form.querySelectorAll(".invalid-feedback").forEach((el) =>
                    el.remove()
                );

                const requiredFields = [
                    "name",
                    "email",
                    "address",
                    "city",
                    "state",
                    "fundraising_goal",
                    "fund_raise_message",
                ];
                let hasErrors = false;
                let firstInvalidInput = null;

                requiredFields.forEach((field) => {
                    const input = form.querySelector(`[name="${field}"]`);
                    if (input && !input.value.trim()) {
                        showFieldError(input, "This field is required");
                        if (!firstInvalidInput) firstInvalidInput = input;
                        hasErrors = true;
                    }
                });

                if (hasErrors && firstInvalidInput) {
                    firstInvalidInput.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                    });
                    firstInvalidInput.focus({ preventScroll: true });
                }

                if (hasErrors) {
                    resetButton();
                    return;
                }
                let url = route("admin.storeEditedUser");
                try {
                    const response = await fetch(url, {
                        method: "POST",
                        body: formData,
                    });

                    const data = await response.json();

                    if (!response.ok) {
                        if (data.errors) {
                            Object.entries(data.errors).forEach(
                                ([field, messages]) => {
                                    const input = form.querySelector(
                                        `[name="${field}"]`
                                    );
                                    if (input)
                                        showFieldError(input, messages[0]);
                                }
                            );
                        } else {
                            createSlider(
                                "error",
                                "Something went wrong. Please try again.",
                                { title: "Error" }
                            );
                        }
                    } else {
                        createSlider("success", "User updated successfully!", {
                            title: "Success",
                        });
                        setTimeout(() => {
                            modal.hide();

                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        }, 1000);
                    }
                } catch (error) {
                    createSlider("error", "Server error. Please try again.", {
                        title: "Error",
                    });
                }

                resetButton();

                function resetButton() {
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                }

                function showFieldError(input, message) {
                    input.classList.add("is-invalid");
                    const error = document.createElement("div");
                    error.className = "invalid-feedback";
                    error.innerText = message;
                    input.parentNode.appendChild(error);
                }
            });
        } catch (err) {
            console.error(err);
            container.innerHTML = `
                <div class="alert alert-danger mt-4">
                    Failed to load form. Please try again.
                </div>
            `;
        }
    });

    // Admin Registration Form
    const adminForm = document.getElementById("addAdminForm");
    if (adminForm) {
        adminForm.addEventListener("submit", async function (event) {
            event.preventDefault();

            const formData = new FormData(adminForm);
            if (!validateForm(formData)) return;

            const button = document.getElementById("createAdminButton");
            const url = route("admin.addAdminAccount");

            await submitFormData(
                url,
                formData,
                button,
                "Admin account created successfully",
                "Creating admin...",
                "addAdminModal"
            );
        });
    }

    function showLoader(button, loadingText = "Processing...") {
        button.disabled = true;
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = `
<div class="d-flex align-items-center">
    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
    <span>${loadingText}</span>
</div>
`;
        button.classList.add("btn-pulse");
    }

    /**
     * Utility function to hide loading state on a button
     * @param {HTMLElement} button - The button to restore
     */
    function hideLoader(button) {
        button.disabled = false;
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.classList.remove("btn-pulse");
    }

    /**
     * Utility function to handle form validation
     * @param {FormData} formData - The form data to validate
     * @returns {boolean} - Whether the form is valid
     */
    function validateForm(formData) {
        let isValid = true;

        formData.forEach((value, key) => {
            const input = document.getElementById(key);
            if (!input) return;

            input.classList.remove("is-invalid");

            if (value === "") {
                input.classList.add("is-invalid");
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * Handle API error responses
     * @param {Response} response - The fetch response object
     * @returns {Promise} - Promise that resolves with parsed data or rejects with error
     */
    async function handleApiResponse(response) {
        const data = await response.json();

        if (!response.ok) {
            // Handle validation errors (422) or other errors
            if (response.status === 422 && data.errors) {
                const errorMessages = Object.values(data.errors)
                    .map((messages) => messages.join(", "))
                    .join("\n");
                throw new Error(errorMessages);
            }

            throw new Error(data.message || "Something went wrong");
        }

        return data;
    }

    /**
     * Submit form data to an API endpoint
     * @param {string} url - The API endpoint URL
     * @param {FormData} formData - The form data to submit
     * @param {HTMLElement} button - The submit button
     * @param {string} successMessage - Message to show on success
     * @param {string} loadingText - Text to show while loading
     * @param {string} modalId - ID of modal to close on success (optional)
     * @returns {Promise} - Promise that resolves with the API response
     */
    async function submitFormData(
        url,
        formData,
        button,
        successMessage,
        loadingText,
        modalId = null
    ) {
        try {
            showLoader(button, loadingText);

            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": document.querySelector(
                        'meta[name="csrf-token"]'
                    ).content,
                },
                body: formData,
            });

            const data = await handleApiResponse(response);

            createSlider("success", successMessage, { title: "Success" });

            button.form.reset();

            if (modalId) {
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(
                        document.getElementById(modalId)
                    );
                    if (modal) modal.hide();
                }, 1500);
            }

            return data;
        } catch (error) {
            createSlider("error", error.message, { title: "Error" });
            return null;
        } finally {
            hideLoader(button);
        }
    }

    // Admin Cropping Functionality
    let adminCropper = null;
    let originalAdminFile = null;

    function initializeAdminCropper(file, previewImg, fileInput) {
        originalAdminFile = file;
        const cropModal = document.getElementById("imageCropModal");
        const cropImage = document.getElementById("cropImage");
        const rotationSlider = document.getElementById("rotationSlider");
        const zoomSlider = document.getElementById("zoomSlider");
        const resetCropBtn = document.getElementById("resetCropBtn");
        const applyCropBtn = document.getElementById("applyCropBtn");

        const reader = new FileReader();

        reader.onload = function (e) {
            cropImage.src = e.target.result;

            // Show loading overlay
            const cropContainer = document.getElementById("cropContainer");
            const loadingOverlay = document.createElement("div");
            loadingOverlay.className = "crop-loading";
            loadingOverlay.innerHTML =
                '<div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>';
            cropContainer.appendChild(loadingOverlay);

            // Initialize cropper after image loads
            cropImage.onload = function () {
                loadingOverlay.remove();

                // Ensure image is properly sized
                cropImage.style.maxWidth = "100%";
                cropImage.style.maxHeight = "100%";
                cropImage.style.width = "auto";
                cropImage.style.height = "auto";

                // Show the crop modal with proper error handling
                try {
                    const cropModalInstance = new bootstrap.Modal(cropModal, {
                        backdrop: true,
                        keyboard: true,
                        focus: true,
                    });

                    // Initialize cropper after modal is shown
                    cropModal.addEventListener(
                        "shown.bs.modal",
                        function () {
                            // Small delay to ensure modal is fully rendered
                            setTimeout(() => {
                                initializeAdminCropperInstance();
                            }, 100);
                        },
                        { once: true }
                    ); // Only listen once

                    cropModalInstance.show();
                } catch (error) {
                    console.error("Modal initialization error:", error);
                    // Fallback: try to show modal using jQuery if available
                    if (typeof $ !== "undefined" && $.fn.modal) {
                        $(cropModal).modal("show");
                        $(cropModal).on("shown.bs.modal", function () {
                            setTimeout(() => {
                                initializeAdminCropperInstance();
                            }, 100);
                        });
                    } else {
                        // Last resort: show modal manually
                        cropModal.style.display = "block";
                        cropModal.classList.add("show");
                        document.body.classList.add("modal-open");

                        // Add backdrop manually
                        const backdrop = document.createElement("div");
                        backdrop.className = "modal-backdrop fade show";
                        document.body.appendChild(backdrop);

                        // Initialize cropper after manual modal show
                        setTimeout(() => {
                            initializeAdminCropperInstance();
                        }, 100);
                    }
                }
            };
        };

        reader.readAsDataURL(originalAdminFile);

        function initializeAdminCropperInstance() {
            // Destroy existing cropper if any
            if (adminCropper) {
                adminCropper.destroy();
            }

            // Initialize new cropper
            adminCropper = new Cropper(cropImage, {
                aspectRatio: 1,
                viewMode: 0, // Allow the image to fill the container
                dragMode: "move",
                autoCropArea: 1, // Use the full available area
                restore: false,
                guides: true,
                center: true,
                highlight: false,
                cropBoxMovable: true,
                cropBoxResizable: true,
                toggleDragModeOnDblclick: false,
                minCropBoxWidth: 100,
                minCropBoxHeight: 100,
                responsive: true,
                checkCrossOrigin: false,
                ready: function () {
                    // Set initial zoom and rotation
                    adminCropper.zoomTo(zoomSlider.value);
                    adminCropper.rotateTo(rotationSlider.value);

                    // Get container and image data
                    const containerData = adminCropper.getContainerData();
                    const imageData = adminCropper.getImageData();

                    // Calculate optimal zoom to fit image in container
                    const scaleX = containerData.width / imageData.naturalWidth;
                    const scaleY =
                        containerData.height / imageData.naturalHeight;
                    const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%

                    // Apply the calculated scale
                    adminCropper.zoomTo(scale);

                    // Set crop box to use most of the available space
                    const cropSize =
                        Math.min(containerData.width, containerData.height) *
                        0.9;
                    adminCropper.setCropBoxData({
                        width: cropSize,
                        height: cropSize,
                        left: (containerData.width - cropSize) / 2,
                        top: (containerData.height - cropSize) / 2,
                    });

                    // Force a resize to ensure proper rendering
                    setTimeout(() => {
                        adminCropper.render();
                    }, 50);
                },
            });

            // Add resize listener to handle window resize
            const resizeHandler = function () {
                if (adminCropper) {
                    adminCropper.render();
                }
            };

            window.addEventListener("resize", resizeHandler);

            // Store the resize handler for cleanup
            adminCropper.resizeHandler = resizeHandler;
        }

        // Rotation slider
        if (rotationSlider) {
            rotationSlider.addEventListener("input", function () {
                if (adminCropper) {
                    adminCropper.rotateTo(this.value);
                }
            });
        }

        // Zoom slider
        if (zoomSlider) {
            zoomSlider.addEventListener("input", function () {
                if (adminCropper) {
                    adminCropper.zoomTo(this.value);
                }
            });
        }

        // Reset crop button
        if (resetCropBtn) {
            resetCropBtn.addEventListener("click", function () {
                if (adminCropper) {
                    adminCropper.reset();
                    rotationSlider.value = 0;
                    zoomSlider.value = 1;
                    adminCropper.rotateTo(0);
                    adminCropper.zoomTo(1);
                }
            });
        }

        // Apply crop button
        if (applyCropBtn) {
            applyCropBtn.addEventListener("click", function () {
                if (adminCropper) {
                    // Show loading state
                    applyCropBtn.disabled = true;
                    applyCropBtn.innerHTML =
                        '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Processing...';

                    // Get cropped canvas
                    const canvas = adminCropper.getCroppedCanvas({
                        width: 400,
                        height: 400,
                        imageSmoothingEnabled: true,
                        imageSmoothingQuality: "high",
                    });

                    // Convert canvas to blob
                    canvas.toBlob(
                        function (blob) {
                            // Create a new file from the blob
                            const croppedFile = new File(
                                [blob],
                                originalAdminFile.name,
                                {
                                    type: originalAdminFile.type,
                                    lastModified: Date.now(),
                                }
                            );

                            // Create a new FileList-like object
                            const dataTransfer = new DataTransfer();
                            dataTransfer.items.add(croppedFile);
                            fileInput.files = dataTransfer.files;

                            // Update preview image
                            const imageUrl = URL.createObjectURL(blob);

                            // Update profile preview in modal
                            if (previewImg) {
                                previewImg.style.opacity = 0;
                                setTimeout(() => {
                                    previewImg.src = imageUrl;
                                    previewImg.style.opacity = 1;
                                }, 300);
                            }

                            // Close modal and show success message
                            const cropModalInstance =
                                bootstrap.Modal.getInstance(cropModal);
                            if (cropModalInstance) {
                                cropModalInstance.hide();
                            }

                            createSlider(
                                "success",
                                "Profile picture cropped successfully!",
                                { title: "Success" }
                            );

                            // Reset button state
                            setTimeout(() => {
                                applyCropBtn.disabled = false;
                                applyCropBtn.innerHTML =
                                    '<i class="fas fa-check me-2"></i>Apply Crop';
                            }, 1000);
                        },
                        originalAdminFile.type,
                        0.9
                    );
                }
            });
        }

        // Clean up cropper when modal is hidden
        if (cropModal) {
            cropModal.addEventListener("hidden.bs.modal", function () {
                if (adminCropper) {
                    // Remove resize listener if it exists
                    if (adminCropper.resizeHandler) {
                        window.removeEventListener(
                            "resize",
                            adminCropper.resizeHandler
                        );
                    }
                    adminCropper.destroy();
                    adminCropper = null;
                }
                // Reset sliders
                if (rotationSlider) rotationSlider.value = 0;
                if (zoomSlider) zoomSlider.value = 1;
            });
        }
    }
});
