@extends('layouts.app')

@section('title', 'Failed Transaction Details')

@section('no-header-footer', 'true')

@section('content')

    @include('layouts.admin-nav')

    <section>
        <div class="container-fluid px-4 py-5">
            <div class="dashboard-header animated">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="dashboard-title">Failed Transaction Details</h1>
                        <p class="text-white opacity-75">Transaction ID: {{ $failedTransaction->payment_intent_id }}</p>
                    </div>
                    <div class="d-flex gap-3">
                        <a href="{{ route('admin.failed-transactions.index') }}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>

            <div class="row g-4">
                <!-- Transaction Overview -->
                <div class="col-lg-8">
                    <div class="chart-card animated delay-1">
                        <div class="card-header">
                            <h5 class="card-title">Transaction Overview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Donor Information</label>
                                        <div class="p-3 bg-light rounded">
                                            <div class="mb-2">
                                                <strong>Name:</strong>
                                                {{ $failedTransaction->customer_name ?? 'Not provided' }}
                                            </div>
                                            <div class="mb-2">
                                                <strong>Email:</strong>
                                                {{ $failedTransaction->customer_email ?? 'Not provided' }}
                                            </div>
                                            <div class="mb-2">
                                                <strong>Phone:</strong>
                                                {{ $failedTransaction->customer_phone ?? 'Not provided' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Payment Details</label>
                                        <div class="p-3 bg-light rounded">
                                            <div class="mb-2">
                                                <strong>Amount:</strong>
                                                <span
                                                    class="text-danger fw-bold">${{ number_format($failedTransaction->amount, 2) }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Amount Charged:</strong>
                                                <span
                                                    class="text-warning">${{ number_format($failedTransaction->amount_charged, 2) }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Currency:</strong> {{ strtoupper($failedTransaction->currency) }}
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Failure Information</label>
                                        <div class="p-3 bg-light rounded">
                                            <div class="mb-2">
                                                <strong>Status:</strong>
                                                <span class="badge status-{{ $failedTransaction->status }}">
                                                    {{ ucfirst(str_replace('_', ' ', $failedTransaction->status)) }}
                                                </span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Failure Stage:</strong>
                                                {{ ucfirst(str_replace('_', ' ', $failedTransaction->failure_stage)) }}
                                            </div>
                                            <div class="mb-2">
                                                <strong>Failure Code:</strong> {{ $failedTransaction->failure_code }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">Timeline</label>
                                        <div class="p-3 bg-light rounded">
                                            <div class="mb-2">
                                                <strong>Failed At:</strong>
                                                {{ $failedTransaction->failed_at->format('M d, Y H:i:s') }}
                                            </div>
                                            @if ($failedTransaction->resolved_at)
                                                <div class="mb-2">
                                                    <strong>Resolved At:</strong>
                                                    {{ $failedTransaction->resolved_at->format('M d, Y H:i:s') }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if ($failedTransaction->donation_message)
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Donation Message</label>
                                    <div class="p-3 bg-light rounded">
                                        <em>"{{ $failedTransaction->donation_message }}"</em>
                                    </div>
                                </div>
                            @endif

                            @if ($failedTransaction->failure_message)
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Failure Message</label>
                                    <div class="p-3 bg-light rounded">
                                        <code>{{ $failedTransaction->failure_message }}</code>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Transaction Info -->
                <div class="col-lg-4">
                    <div class="chart-card animated delay-2">
                        <div class="card-header">
                            <h5 class="card-title">Transaction Info</h5>
                        </div>
                        <div class="card-body">
                            <div class="small text-muted">
                                <div class="mb-2"><strong>Payment Intent:</strong>
                                    {{ $failedTransaction->payment_intent_id }}</div>
                                @if ($failedTransaction->customer_id)
                                    <div class="mb-2"><strong>Customer ID:</strong> {{ $failedTransaction->customer_id }}
                                    </div>
                                @endif
                                @if ($failedTransaction->subscription_id)
                                    <div class="mb-2"><strong>Subscription ID:</strong>
                                        {{ $failedTransaction->subscription_id }}</div>
                                @endif
                                <div class="mb-2"><strong>Retry Count:</strong> {{ $failedTransaction->retry_count }}/3
                                </div>
                            </div>
                        </div>
                    </div>

                    @if ($failedTransaction->user)
                        <div class="chart-card animated delay-3 mt-4">
                            <div class="card-header">
                                <h5 class="card-title">Donated To</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="donor-avatar primary me-3">
                                        {{ substr($failedTransaction->user->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h6 class="mb-1">{{ $failedTransaction->user->name }}</h6>
                                        <small class="text-muted">{{ $failedTransaction->user->email }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Processing Log -->
            @if ($failedTransaction->processing_log && count($failedTransaction->processing_log) > 0)
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="chart-card animated delay-4">
                            <div class="card-header">
                                <h5 class="card-title">Processing Log</h5>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    @foreach ($failedTransaction->processing_log as $log)
                                        <div class="timeline-item">
                                            <div class="timeline-marker"></div>
                                            <div class="timeline-content">
                                                <div class="d-flex justify-content-between">
                                                    <strong>{{ ucfirst($log['stage']) }}</strong>
                                                    <small
                                                        class="text-muted">{{ \Carbon\Carbon::parse($log['timestamp'])->format('M d, Y H:i:s') }}</small>
                                                </div>
                                                <p class="mb-1">{{ $log['message'] }}</p>
                                                @if (isset($log['data']) && !empty($log['data']))
                                                    <small class="text-muted">
                                                        <code>{{ json_encode($log['data'], JSON_PRETTY_PRINT) }}</code>
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>



@endsection

@push('styles')
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-retry_pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-retry_processing {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-resolved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-abandoned {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 5px;
            width: 12px;
            height: 12px;
            background-color: #F05522;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 0 0 3px #F05522;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #F05522;
        }

        .donor-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .donor-avatar.primary {
            background: linear-gradient(135deg, #19345E, #F05522);
        }
    </style>

    @vite('resources/css/pages/failed-transactions.css')
@endpush

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize animations
            const animatedElements = document.querySelectorAll('.animated');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = 1;
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            animatedElements.forEach(element => {
                element.style.opacity = 0;
                element.style.transform = 'translateY(20px)';
                observer.observe(element);
            });
        });
    </script>
@endpush
