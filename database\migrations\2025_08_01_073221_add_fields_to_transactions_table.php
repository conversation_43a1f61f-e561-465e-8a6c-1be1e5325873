<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            if (!Schema::hasColumn('transactions', 'donor_id')) {
                $table->foreignId('donor_id')->nullable()->after('id')
                    ->constrained('donors')->nullOnDelete();
            }

            if (!Schema::hasColumn('transactions', 'charge_id')) {
                $table->string('charge_id')->nullable()->after('payment_intent_id')->unique();
            }

            if (!Schema::hasColumn('transactions', 'payment_method_details')) {
                $table->json('payment_method_details')->nullable()->after('payment_method');
            }

            if (!Schema::hasColumn('transactions', 'billing_details')) {
                $table->json('billing_details')->nullable()->after('payment_method_details');
            }

            if (!Schema::hasColumn('transactions', 'outcome')) {
                $table->json('outcome')->nullable()->after('billing_details');
            }

            if (!Schema::hasColumn('transactions', 'receipt_url')) {
                $table->string('receipt_url')->nullable()->after('description');
            }

            if (!Schema::hasColumn('transactions', 'receipt_number')) {
                $table->string('receipt_number')->nullable()->after('receipt_url');
            }

            if (!Schema::hasColumn('transactions', 'network_transaction_id')) {
                $table->string('network_transaction_id')->nullable()->after('receipt_number');
            }

            if (!Schema::hasColumn('transactions', 'captured')) {
                $table->boolean('captured')->default(true)->after('network_transaction_id');
            }

            if (!Schema::hasColumn('transactions', 'transaction_date')) {
                $table->timestamp('transaction_date')->nullable()->after('refunded_at');
            }

            if (!Schema::hasColumn('transactions', 'donation_token')) {
                $table->string('donation_token')->nullable()->after('transaction_date');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            if (Schema::hasColumn('transactions', 'donor_id')) {
                $table->dropForeign(['donor_id']);
            }
            $table->dropColumn([
                'donor_id',
                'charge_id',
                'payment_method_details',
                'billing_details',
                'outcome',
                'receipt_url',
                'receipt_number',
                'network_transaction_id',
                'captured',
                'transaction_date',
                'donation_token',
            ]);
        });
    }
};
