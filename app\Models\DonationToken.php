<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DonationToken extends Model
{
    protected $fillable = [
        'token',
        'amount',
        'user_slug',
        'expires_at',
        'used_at',
        'status',
        'failure_reason',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
        'amount' => 'decimal:2'
    ];

    /**
     * Generate a new donation token
     */
    public static function generateToken($amount, $userSlug, $expirationMinutes = 30)
    {
        $token = Str::random(32);

        return self::create([
            'token' => $token,
            'amount' => $amount,
            'user_slug' => $userSlug,
            'expires_at' => Carbon::now()->addMinutes($expirationMinutes),
            'status' => 'pending'
        ]);
    }

    /**
     * Find a valid token
     */
    public static function findValidToken($token)
    {
        return self::where('token', $token)
            ->where('expires_at', '>', Carbon::now())
            ->where(function($query) {
                $query->whereNull('used_at')
                      ->orWhere('status', 'failed'); // Allow failed tokens to be retried
            })
            ->first();
    }

    /**
     * Find a token that can be used for payment (not expired, not successfully used)
     */
    public static function findTokenForPayment($token)
    {
        return self::where('token', $token)
            ->where('expires_at', '>', Carbon::now())
            ->where(function($query) {
                $query->whereNull('used_at')
                      ->orWhere('status', 'failed'); // Allow failed tokens to be retried
            })
            ->first();
    }

    /**
     * Mark token as successfully used
     */
    public function markAsSuccess()
    {
        $this->update([
            'status' => 'success',
            'used_at' => now(),
            'failure_reason' => null
        ]);
    }

    /**
     * Mark token as failed with reason
     */
    public function markAsFailed($failureReason = null)
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $failureReason,
            'used_at' => null // Don't mark as used so it can be retried
        ]);
    }

    /**
     * Generate a new token for retry after failure
     */
    public function regenerateForRetry()
    {
        $newToken = Str::random(32);

        return self::create([
            'token' => $newToken,
            'amount' => $this->amount,
            'user_slug' => $this->user_slug,
            'expires_at' => Carbon::now()->addMinutes(30),
            'status' => 'pending'
        ]);
    }

    /**
     * Clean up expired tokens
     */
    public static function cleanupExpired()
    {
        return self::where('expires_at', '<', Carbon::now())->delete();
    }

    /**
     * Check if token is expired
     */
    public function isExpired()
    {
        return $this->expires_at < Carbon::now();
    }

    /**
     * Check if token has been successfully used
     */
    public function isSuccessfullyUsed()
    {
        return $this->status === 'success' && $this->used_at !== null;
    }

    /**
     * Check if token has been used (either success or failed)
     */
    public function isUsed()
    {
        return $this->used_at !== null;
    }

    /**
     * Check if token can be retried (failed status)
     */
    public function canBeRetried()
    {
        return $this->status === 'failed' && !$this->isExpired();
    }

    /**
     * Get the user associated with this token
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_slug', 'slug');
    }
}
