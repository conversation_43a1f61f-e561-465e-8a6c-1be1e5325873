<?php

namespace App\Http\Controllers;

use App\Jobs\HandleStripePaymentSucceeded;
use App\Jobs\HandleStripePaymentFailed;
use App\Jobs\HandleStripeSubscriptionCancelled;
use App\Jobs\HandleStripeSubscriptionUpdated;
use App\Models\Donor;
use App\Models\Transaction;
use App\Services\FailedTransactionService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use \Stripe\Webhook;
use \Stripe\Stripe;
use Illuminate\Http\Request;
use Exception;

class StripeWebhookController extends Controller
{
    protected $failedTransactionService;

    public function __construct(FailedTransactionService $failedTransactionService)
    {
        $this->failedTransactionService = $failedTransactionService;
    }

    public function handle(Request $request)
    {
        Log::info("Stripe webhook route hit");
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $secret = config('services.stripe.webhook.secret');

        Stripe::setApiKey(config('services.stripe.secret'));

        try {
            if (app()->environment('local')) {
                $event = \Stripe\Event::constructFrom(json_decode($payload, true));
            } else {
                $event = Webhook::constructEvent($payload, $sigHeader, $secret);
            }

            Log::info('Received Stripe webhook event: ' . $event->type, [
                'event_id' => $event->id,
                'event_type' => $event->type
            ]);

            match ($event->type) {

                'charge.succeeded' => $this->handleChargeSucceeded($event),
                'invoice.payment_succeeded' => HandleStripePaymentSucceeded::dispatch($event->data->object),
                'invoice.payment_failed' => $this->handleInvoicePaymentFailed($event),
                'payment_intent.payment_failed' => $this->handlePaymentIntentFailed($event),
                'payment_intent.succeeded' => $this->handlePaymentIntentSucceeded($event),
                'customer.subscription.deleted' => HandleStripeSubscriptionCancelled::dispatch($event->data->object),
                'customer.subscription.updated' => HandleStripeSubscriptionUpdated::dispatch($event->data->object),
                default => Log::info('Unhandled Stripe event: ' . $event->type),
            };

            return response('Webhook handled', 200);
        } catch (Exception $e) {
            // Record webhook processing failure
            $this->failedTransactionService->recordWebhookProcessingFailure(
                ['payload' => $payload, 'headers' => $request->headers->all()],
                $e,
                ['webhook_type' => 'general_processing_error']
            );

            Log::error('Stripe webhook error: ' . $e->getMessage(), [
                'payload' => $payload,
                'error' => $e
            ]);
            return response('Webhook error: ' . $e->getMessage(), 400);
        }
    }

    /**
     * Handle invoice payment failed event
     */
    private function handleInvoicePaymentFailed($event)
    {
        try {
            $invoice = $event->data->object;

            // Record the failed subscription payment
            $this->failedTransactionService->recordStripePaymentFailure(
                $event->toArray(),
                [
                    'webhook_event_id' => $event->id,
                    'webhook_event_type' => $event->type,
                    'failure_stage' => 'webhook_invoice_payment_failed',
                    'subscription_id' => $invoice->subscription ?? null,
                    'invoice_id' => $invoice->id,
                ]
            );

            Log::info('Invoice payment failed recorded', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->subscription ?? null,
                'customer_id' => $invoice->customer ?? null,
                'amount_due' => $invoice->amount_due,
            ]);

            // Dispatch the existing job for backward compatibility
            HandleStripePaymentFailed::dispatch($invoice);
        } catch (Exception $e) {
            // Record webhook processing failure
            $this->failedTransactionService->recordWebhookProcessingFailure(
                $event->toArray(),
                $e,
                ['webhook_type' => 'invoice_payment_failed_processing']
            );

            Log::error('Failed to process invoice payment failed webhook: ' . $e->getMessage(), [
                'event_id' => $event->id,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Handle payment intent failed event
     */
    private function handlePaymentIntentFailed($event)
    {
        try {
            $paymentIntent = $event->data->object;

            // Record the failed payment
            $this->failedTransactionService->recordStripePaymentFailure(
                $event->toArray(),
                [
                    'webhook_event_id' => $event->id,
                    'webhook_event_type' => $event->type,
                    'failure_stage' => 'webhook_payment_intent_failed',
                ]
            );

            Log::info('Payment intent failed recorded', [
                'payment_intent_id' => $paymentIntent->id,
                'failure_code' => $paymentIntent->last_payment_error['code'] ?? null,
                'failure_message' => $paymentIntent->last_payment_error['message'] ?? null,
            ]);

            // Handle donation token failure if this is a one-time donation
            $this->handleDonationTokenFailure($paymentIntent);
        } catch (Exception $e) {
            // Record webhook processing failure
            $this->failedTransactionService->recordWebhookProcessingFailure(
                $event->toArray(),
                $e,
                ['webhook_type' => 'payment_intent_failed_processing']
            );

            Log::error('Failed to process payment intent failed webhook: ' . $e->getMessage(), [
                'event_id' => $event->id,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Handle payment intent succeeded event
     */
    private function handlePaymentIntentSucceeded($event)
    {
        try {
            $paymentIntent = $event->data->object;

            Log::info('Payment intent succeeded', [
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $paymentIntent->amount,
                'customer_id' => $paymentIntent->customer ?? null,
            ]);

            // Handle donation token success if this is a one-time donation
            $this->handleDonationTokenSuccess($paymentIntent);

            // we can add additional logic here for successful payments
            // For example, updating transaction status, sending notifications, etc.
        } catch (Exception $e) {
            Log::error('Failed to process payment intent succeeded webhook: ' . $e->getMessage(), [
                'event_id' => $event->id,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Handle donation token success from webhook
     */
    private function handleDonationTokenSuccess($paymentIntent)
    {
        try {
            // Check if this payment intent has a donation token in metadata
            $donationToken = $paymentIntent->metadata->donation_token ?? null;

            if ($donationToken) {
                $token = \App\Models\DonationToken::where('token', $donationToken)->first();

                if ($token && $token->status === 'pending') {
                    $token->markAsSuccess();

                    Log::info('Donation token marked as successful via webhook', [
                        'token' => $donationToken,
                        'payment_intent_id' => $paymentIntent->id,
                        'amount' => $paymentIntent->amount / 100
                    ]);
                }
            }
        } catch (Exception $e) {
            Log::error('Failed to handle donation token success in webhook: ' . $e->getMessage(), [
                'payment_intent_id' => $paymentIntent->id,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Handle donation token failure from webhook
     */
    private function handleDonationTokenFailure($paymentIntent)
    {
        try {
            // Check if this payment intent has a donation token in metadata
            $donationToken = $paymentIntent->metadata->donation_token ?? null;

            if ($donationToken) {
                $token = \App\Models\DonationToken::where('token', $donationToken)->first();

                if ($token && $token->status === 'pending') {
                    $failureReason = $paymentIntent->last_payment_error['message'] ?? 'Payment failed';
                    $token->markAsFailed($failureReason);

                    Log::info('Donation token marked as failed via webhook', [
                        'token' => $donationToken,
                        'payment_intent_id' => $paymentIntent->id,
                        'failure_reason' => $failureReason
                    ]);
                }
            }
        } catch (Exception $e) {
            Log::error('Failed to handle donation token failure in webhook: ' . $e->getMessage(), [
                'payment_intent_id' => $paymentIntent->id,
                'exception' => $e,
            ]);
        }
    }


    private function handleChargeSucceeded($event)
    {
        try {
            $charge = $event->data->object;

            DB::transaction(function () use ($charge) {

                $transaction = Transaction::where('payment_intent_id', $charge->payment_intent)->first();


                log::info('charge: ', [
                    'charge' => $charge,
                ]);
                if ($transaction) {

                    $transaction->update([
                        'payment_intent_id' => $charge->payment_intent,
                        'charge_id' => $charge->id ?? null,
                        'amount' => $charge->amount / 100,
                        'currency' => $charge->currency,
                        'status' => 'succeeded',
                        'payment_method' => $charge->payment_method,
                        'payment_method_details' => [
                            'brand' => $charge->payment_method_details->card->brand ?? null,
                            'last4' => $charge->payment_method_details->card->last4 ?? null,
                            'exp_month' => $charge->payment_method_details->card->exp_month ?? null,
                            'exp_year' => $charge->payment_method_details->card->exp_year ?? null,
                        ],
                        'description' => $charge->description,
                        'metadata' => $charge->metadata->toArray() ?? null,
                        'receipt_url' => $charge->receipt_url ?? null,
                        'receipt_number' => $charge->receipt_number ?? null,
                        'transaction_date' => now(),
                    ]);
                    // in case if there is no transaction store due to some processing error
                    // we will store the transaction here because the event is charge succeeded
                    // and the payment is successful but our system has not stored it for any reason

                    if (!$transaction) {

                        $transaction = Transaction::create([
                            'payment_intent_id' => $charge->payment_intent,
                            'charge_id' => $charge->id ?? null,
                            'amount' => $charge->amount / 100,
                            'currency' => $charge->currency,
                            'status' => 'succeeded',
                            'payment_method' => $charge->payment_method,
                            'payment_method_details' => [
                                'brand' => $charge->payment_method_details->card->brand ?? null,
                                'last4' => $charge->payment_method_details->card->last4 ?? null,
                                'exp_month' => $charge->payment_method_details->card->exp_month ?? null,
                                'exp_year' => $charge->payment_method_details->card->exp_year ?? null,
                            ],
                            'description' => $charge->description,
                            'metadata' => $charge->metadata->toArray() ?? null,
                            'receipt_url' => $charge->receipt_url ?? null,
                            'receipt_number' => $charge->receipt_number ?? null,
                            'transaction_date' => now(),
                        ]);
                    }


                    log::info('metadata', [
                        'metadata' => $charge->metadata->toArray() ?? null,
                    ]);



                    Log::info('Charge succeeded and stored', [
                        'charge_id' => $charge->id,

                        'amount' => $charge->amount / 100,
                        'customer_id' => $charge->customer,
                        'receipt_url' => $charge->receipt_url ?? null,
                        'receipt_number' => $charge->receipt_number ?? null,
                    ]);
                } else {
                    Log::warning('Charge succeeded but no donor found', [
                        'charge_id' => $charge->id,
                        'customer_id' => $charge->customer,
                        'amount' => $charge->amount,
                    ]);
                }
            });
        } catch (Exception $e) {
            Log::error('Failed to process charge succeeded webhook: ' . $e->getMessage(), [
                'event_id' => $event->id,
                'exception' => $e,
            ]);
        }
    }
}
