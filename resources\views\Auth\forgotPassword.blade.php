@extends('layouts.app')

@section('title', 'Forgot Password')
@section('content')

<section class="hero hero--title">
    <div class="container-fluid gx-0">
        <div class="bg-blue text-center text-uppercase">
            <h1 class="mb-0">Forgot Password</h1>
        </div>
    </div>
</section>
<section class="sec content-row">
    <div class="container">
        <div class="copy text-center">
            <p>
                No worries! We'll help you reset your password.<br class="d-none d-md-block" />
                Enter your email address below and we'll send you instructions.
            </p>
        </div>
    </div>
</section>
<section class="form">
    <div class="container">
        @if (session('success'))
        <div class="alert alert-success fade show" role="alert">
            {{session('success')}}
        </div>
        @endif
        <form class="row login-form gx-lg-5" action="{{ route('forgotPassword') }}" method="POST">
            @csrf
            <div class="col-md-12 mb-4">
                <label class="form-label" for="email">Email</label>
                <input class="form-control" id="email" type="email" name="email" placeholder="Enter your email" required />
                @if ($errors->any())
                    @foreach ($errors->all() as $error)
                    <div class="invalid-feedback d-block">{{ $error }}</div>
                    @endforeach
                @endif
            </div>
            <div class="col-md-12 my-4 text-center">
                <button class="cta orange border-0 hoverbutton" type="submit"><span>Send Reset Link</span></button>
            </div>
            <div class="col-md-12 text-center">
                <p>Remember your password? <a href="{{ route('login') }}" class="text-decoration-none fw-bold">Back to Login</a></p>
            </div>
        </form>
    </div>
</section>

@endsection

@push('styles')
@vite('resources/css/pages/login.css')
@endpush
