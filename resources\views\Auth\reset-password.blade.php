@extends('layouts.app')

@section('title', 'Reset Password')
@section('content')

<section class="hero hero--title">
    <div class="container-fluid gx-0">
        <div class="bg-blue text-center text-uppercase">
            <h1 class="mb-0">Reset Password</h1>
        </div>
    </div>
</section>
<section class="sec content-row">
    <div class="container">
        <div class="copy text-center">
            <p>
                Create a new secure password for your account.<br class="d-none d-md-block" />
                Make sure it's at least 8 characters long.
            </p>
        </div>
    </div>
</section>
<section class="form">
    <div class="container">
        @if (session('success'))
        <div class="alert alert-success fade show" role="alert">
            {{session('success')}}
        </div>
        @endif
        <form class="row login-form gx-lg-5" action="{{ route('password.update') }}" method="POST">
            @csrf
            <div class="col-md-12 mb-4">
                <label class="form-label" for="email">Email</label>
                <input class="form-control" id="email" type="email" name="email" value="{{ request('email') }}" required />
                @error('email')
                <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-12 mb-4 password-container">
                <label class="form-label" for="newPassword">New Password</label>
                <input class="form-control" id="newPassword" type="password" name="password" required placeholder="Enter new password" />
                @error('password')
                <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-12 mb-4 password-container">
                <label class="form-label" for="confirmPassword">Confirm Password</label>
                <input class="form-control" id="confirmPassword" type="password" name="password_confirmation" required placeholder="Confirm new password" />
                @error('password_confirmation')
                <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
            <input type="hidden" name="token" value="{{$token}}">
            <div class="col-md-12 my-4 text-center">
                <button class="cta orange border-0 hoverbutton" type="submit"><span>Reset Password</span></button>
            </div>
            <div class="col-md-12 text-center">
                <p>Remember your password? <a href="{{ route('login') }}" class="text-decoration-none fw-bold">Back to Login</a></p>
            </div>
        </form>
    </div>
</section>

@endsection

@push('styles')
@vite('resources/css/pages/login.css')
@endpush
