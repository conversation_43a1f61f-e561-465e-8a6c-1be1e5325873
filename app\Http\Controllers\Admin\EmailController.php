<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\CustomEmail;
use Illuminate\Support\Facades\Storage;
use App\Models\EmailTemplate;
use App\Models\User;
use Carbon\Carbon;

class EmailController extends Controller
{
    public function showEmailBuilder(Request $request)
    {
        $templates = EmailTemplate::where('is_template', true)->get();


        $selectedTemplate = null;


        if ($request->has('template')) {
            $templateId = $request->query('template');
            $selectedTemplate = EmailTemplate::find($templateId);
        }

        return view('Admin.send-mail', compact('templates', 'selectedTemplate'));
    }

    public function sendEmail(Request $request)
    {
        $request->validate([
            'template_name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'html_content' => 'required|string',
            'recipient_group' => 'required|string',
        ]);



        $htmlContent = $request->html_content;
        $subject = $request->subject;

        // dd($request->recipient_group);
        $recipientGroup = $request->recipient_group;


        $recipients = $this->getRecipientsByGroup($recipientGroup);

        // dd($recipients);


        if (empty($recipients)) {
            $recipients = [config('mail.from.address')];
            return redirect()->back()->with('error', 'No recipients found in the selected group. Email sent to default address for testing.');
        }


        foreach ($recipients as $recipient) {
            Mail::to($recipient)->send(new CustomEmail($subject, $htmlContent));
        }


        EmailTemplate::create([
            'name' => $request->template_name,
            'category' => 'sent',
            'description' => 'Sent on ' . now()->format('Y-m-d H:i:s'),
            'content' => $htmlContent,
            'subject' => $subject,
            'recipient_group' => $recipientGroup,
            'is_template' => false,
        ]);

        return redirect()->back()->with('success', 'Email sent successfully to ' . count($recipients) . ' recipients!');
    }

    public function uploadImage(Request $request)
    {
        $request->validate([
            'file' => 'required|image|max:2048',
        ]);

        $file = $request->file('file');
        $path = $file->store('email-images', 'public');

        return response()->json([
            'location' => asset('storage/' . $path)
        ]);
    }

    public function saveTemplate(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'description' => 'nullable|string',
            'content' => 'required|string',
            'subject' => 'nullable|string|max:255',
            'recipient_group' => 'nullable|string',
        ]);

        EmailTemplate::create([
            'name' => $request->name,
            'category' => $request->category,
            'description' => $request->description,
            'content' => $request->content,
            'subject' => $request->subject,
            'recipient_group' => $request->recipient_group,
            'is_template' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Template saved successfully'
        ]);
    }


    public function getTemplate($id)
    {
        $template = EmailTemplate::findOrFail($id);
        return response()->json($template);
    }


    public function getTemplates()
    {
        $templates = EmailTemplate::where('is_template', true)->get();
        return response()->json($templates);
    }


    public function showTemplates()
    {
        $templates = EmailTemplate::orderBy('created_at', 'desc')->get();
        return view('Admin.email-templates', compact('templates'));
    }


    private function getRecipientsByGroup($group)
    {
        $recipients = [];

        switch ($group) {
            case 'subscribers':

                $subscribers = \App\Models\SubscriberForLatestEvents::where('active', true)->pluck('email')->toArray();
                $recipients = array_merge($recipients, $subscribers);
                break;

            case 'athletes':

                $athletes = User::where('is_admin', false)->pluck('email')->toArray();
                $recipients = array_merge($recipients, $athletes);
                break;

            case 'athletes_completed_goals':

                $lastWeek = now()->subDays(6)->startOfDay();

                $athletes = User::with(['donors' => function ($query) {
                    $query->orderBy('created_at');
                }])
                    ->get()
                    ->filter(function ($user) use ($lastWeek) {
                        $total = $user->donors->sum('amount');

                        if ($total >= $user->fundraising_goal) {
                            $cumulative = 0;
                            foreach ($user->donors as $donor) {
                                $cumulative += $donor->amount;
                                if ($cumulative >= $user->fundraising_goal) {

                                    return Carbon::parse($donor->created_at)->greaterThanOrEqualTo($lastWeek);
                                }
                            }
                        }

                        return false;
                    })
                    ->pluck('email')
                    ->toArray();
                $recipients = array_merge($recipients, $athletes);
                break;

            case 'athletes_incomplete_goals':

                $athletes = User::with('donors')->get()->filter(function ($user) {
                    return $user->donors->sum('amount') < $user->fundraising_goal;
                })->pluck('email')->toArray();

                $recipients = array_merge($recipients, $athletes);
                break;

            default:
                break;
        }

        return array_unique($recipients);
    }
}
