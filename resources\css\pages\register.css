.password-container {
    position: relative;
    display: flex;
    flex-direction: column;
}

.password-container .form-control {
    position: relative;
}

.password-toggle {
    position: absolute;
    top: 32px; /* Align with input field */
    right: 3rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 22px;
    color: #19345e !important;
    z-index: 10;
    padding: 5px;
    line-height: 1;
    border-radius: 50%;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.password-toggle:hover {
    background-color: rgba(106, 17, 203, 0.1);
    color: #19345e !important;
}

.password-toggle:focus {
    outline: none;
    box-shadow: 0 0 6px rgba(106, 17, 203, 0.3);
}

.password-toggle svg {
    width: 24px;
    height: 24px;
    fill: #19345e;
    transition: fill 0.2s ease;
}

.password-toggle:hover svg {
    fill: #19345e;
}

/* CSS Fixes for alignment */
.file-upload-container {
    width: 100%;
    margin-bottom: 20px;
}

.drop-zone {
    position: relative;
    border: 2px dashed #ccc;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    width: 100%; /* Make sure it takes full width */
}

.drop-zone:hover {
    border-color: #999;
    background-color: #f7f7f7;
}

.drop-zone.drag-over {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
}

.drop-zone-text {
    margin: 0;
    color: #666;
    font-size: 16px;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: -1; /* Hide actual input but keep it accessible */
}

.image-preview-container {
    position: relative;
    display: none;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-height: 300px;
    margin-top: 10px;
    border-radius: 5px;
    overflow: hidden;
}

#imagePreview {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
}

.remove-image-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.remove-image-btn:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.remove-icon {
    color: #ff4d4d;
    font-size: 14px;
    font-style: normal;
}

.upload-buttons-container {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.upload-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.upload-btn:hover {
    background-color: #e0e0e0;
}

.upload-icon {
    margin-right: 5px;
    font-style: normal;
}

/* Fix for responsive issues */
@media (max-width: 767px) {
    .col-md-12 {
        padding-left: 15px;
        padding-right: 15px;
    }
}
