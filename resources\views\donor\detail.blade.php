@extends('layouts.app')

<meta name="stripe-key" content="{{ config('services.stripe.key') }}">

@section('title', 'Details')

@section('content')
    <div class="donation-wrapper">
        <div class="donation-container">
            <div class="donation-header">
                <h2 class="donation-title">Make a Difference</h2>
                <p class="donation-subtitle">Your generosity can change lives</p>
            </div>

            <form class="donation-form" id="donationForm" action={{ route('createPaymentIntent') }} method='POST'>
                @csrf
                <input type="hidden" name="amount" value="{{ $amount }}">
                <input type="hidden" name="donatedTo" value="{{ $user->slug }}">
                <input type="hidden" name="donationToken" value="{{ $donationToken->token }}">
                <input type="hidden" id="currentToken" value="{{ $donationToken->token }}">

                <div class="form-grid">
                    <div class="form-section personal-info">
                        <div class="section-header">
                            <i class="fas fa-user-circle"></i>
                            <h3>Personal Details</h3>
                        </div>

                        <div class="name-fields">
                            <div class="form-group">
                                <label for="firstName">First Name <span class="required">*</span></label>
                                <input type="text" id="firstName" name="firstName" required class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name <span class="required">*</span></label>
                                <input type="text" id="lastName" name="lastName" required class="form-control">
                            </div>
                        </div>

                        <div class="form-group full-width">
                            <label for="email">Email Address <span class="required">*</span></label>
                            <input type="email" id="email" name="email" required class="form-control">
                        </div>

                        <div class="name-fields">
                            <div class="form-group">
                                <label for="city">City <span class="required">*</span></label>
                                <input type="text" id="city" name="city" required class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="state">State <span class="required">*</span></label>
                                <input type="text" id="state" name="state" required class="form-control">
                            </div>
                        </div>

                        <div class="form-group full-width">
                            <label for="address"> Billing Address <span class="required">*</span></label>
                            <input type="text" id="address" name="address" required class="form-control">
                        </div>
                    </div>

                    <div class="form-section payment-details">
                        <div class="section-header">
                            <i class="fas fa-credit-card"></i>
                            <h3>Payment Information</h3>
                        </div>

                        <div class="stripe-card-wrapper">
                            <div id="card-element" class="stripe-card-element"></div>
                        </div>

                        <div id="card-errors" class="error-message"></div>

                        <div class="donation-amount">
                            <span class="amount-label">Donation Amount</span>
                            <div class="amount-display">${{ $amount }}</div>
                        </div>
                    </div>

                    <div class="form-section message-section">
                        <div class="section-header">
                            <i class="fas fa-comment-dots"></i>
                            <h3>Your Message</h3>
                        </div>

                        <div class="form-group">
                            <label for="donationMessage">Share Your Inspiration <span
                                    class="optional">(Optional)</span></label>
                            <textarea id="donationMessage" name="message" class="form-control" rows="4"
                                placeholder="Write a message of hope..."></textarea>
                        </div>
                    </div>

                    <div class="form-section privacy-section">
                        <div class="section-header">
                            <i class="fas fa-shield-alt"></i>
                            <h3>Privacy Options</h3>
                        </div>

                        <div class="privacy-options">
                            <div class="checkbox-group">
                                <input type="checkbox" id="anonymousToPublic" name="anonymousToPublic"
                                    class="custom-checkbox">
                                <label for="anonymousToPublic">Visible to recipient, hidden from public</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="completeAnonymity" name="completeAnonymity"
                                    class="custom-checkbox">
                                <label for="completeAnonymity">Complete anonymity</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="donation-submit">
                    <button type="submit" class="btn-donate" id="payment-button">
                        <span class="btn-text">Donate ${{ $amount }}</span>
                        <span class="btn-icon">
                            <i class="fas fa-heart"></i>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('styles')
    @vite('resources/css/pages/card-details.css')
@endpush

@push('scripts')
    <script src="https://js.stripe.com/v3/"></script>

    @vite('resources/js/pages/details.js')
@endpush
