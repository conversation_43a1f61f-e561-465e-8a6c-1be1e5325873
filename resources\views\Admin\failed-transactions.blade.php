@extends('layouts.app')

@section('title', 'Failed Transactions')

@section('no-header-footer', 'true')

@section('content')

    @include('layouts.admin-nav')

    <section>
        <div class="container-fluid px-4 py-5">
            <div class="dashboard-header animated">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="dashboard-title">Failed Transactions</h1>
                        <p class="text-white opacity-75">Monitor and manage failed payment transactions</p>
                    </div>
                    <div>
                        {{-- <button class="export-btn" id="exportFailedTransactions">
                            <i class="fas fa-download"></i> Export Report
                        </button> --}}
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-xl-6 col-md-8 mx-auto">
                    <div class="stat-card animated delay-1">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Failed Transactions</h6>
                                <h2 class="stat-value" id="totalFailed">{{ number_format($stats['total_failed']) }}</h2>
                                <div>
                                    <span class="trend-indicator">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Failed payment transactions
                                    </span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Search and Filter Section -->
            <div class="row justify-content-center mb-4">
                <div class="col-md-12">
                    <div class="search-filter-container p-3 rounded-pill shadow-sm"
                        style="background: linear-gradient(145deg, #f7f9fc, #ffffff); border: 1px solid #19345E;">
                        <div class="row g-2 align-items-center">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text border-0 bg-transparent">
                                        <i class="fas fa-search" style="color: #19345E;"></i>
                                    </span>
                                    <input type="text" id="search"
                                        class="form-control border-0 bg-transparent text-dark"
                                        placeholder="Search by email, name..." value="{{ request('search') }}"
                                        style="box-shadow: none;">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex gap-2">
                                    <input type="date" id="date-from" class="form-control border-0 bg-transparent"
                                        value="{{ request('date_from') }}"
                                        style="box-shadow: none; color: #F05522; font-weight: 500;">
                                    <input type="date" id="date-to" class="form-control border-0 bg-transparent"
                                        value="{{ request('date_to') }}"
                                        style="box-shadow: none; color: #F05522; font-weight: 500;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Failed Transactions Table -->
            <div class="row justify-content-center">
                <div class="col-md-12">
                    <div class="chart-card animated delay-3">
                        <div class="card-header">
                            <h5 class="card-title">Failed Transactions</h5>
                        </div>
                        <div id="table-container" class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table data-table user-table">
                                    <thead>
                                        <tr>
                                            <th class="py-3 px-4">Donor</th>
                                            <th class="py-3 px-4">Amount</th>
                                            <th class="py-3 px-4 text-center">Status</th>
                                            <th class="py-3 px-4">Failure Message</th>
                                            <th class="py-3 px-4">Failure code</th>
                                            <th class="py-3 px-4">Failed At</th>
                                            <th class="py-3 px-4">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="failed-transactions-table-body">
                                        @if ($failedTransactions->count() > 0)
                                            @foreach ($failedTransactions as $transaction)
                                                <tr>
                                                    <td class="py-3 px-4">
                                                        <div class="d-flex align-items-center">
                                                            <div class="donor-avatar primary">
                                                                {{ substr($transaction->customer_name ?? 'U', 0, 1) }}
                                                            </div>
                                                            <div>
                                                                <div class="fw-semibold">
                                                                    {{ $transaction->customer_name ?? 'Unknown' }}</div>
                                                                <small
                                                                    class="text-muted">{{ $transaction->customer_email ?? 'No email' }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-3 px-4">
                                                        <div class="fw-semibold" style="color: #F05522;">
                                                            ${{ number_format($transaction->amount, 2) }}</div>
                                                        @if ($transaction->amount_charged > 0)
                                                            <small class="text-warning">Charged:
                                                                ${{ number_format($transaction->amount_charged, 2) }}</small>
                                                        @endif
                                                    </td>
                                                    <td class="py-3 px-4 text-center">
                                                        <span
                                                            class="status-badge status-{{ $transaction->status }}">{{ ucfirst(str_replace('_', ' ', $transaction->status)) }}</span>
                                                    </td>
                                                    <td class="py-3 px-4">
                                                        <small>{{ $transaction->failure_message ?? 'Unknown' }}</small>
                                                    </td>
                                                    <td class="py-3 px-4">
                                                        <small
                                                            title="{{ $transaction->code ?? 'No message' }}">{{ $transaction->failure_code ?? 'Unknown' }}</small>
                                                    </td>
                                                    <td class="py-3 px-4">
                                                        <small>{{ $transaction->failed_at ? $transaction->failed_at->format('M d, Y') : 'Unknown' }}</small>
                                                    </td>
                                                    <td class="py-3 px-4">
                                                        <div class="d-flex flex-wrap gap-1">
                                                            <button class="btn btn-sm action-btn"
                                                                onclick="viewTransaction({{ $transaction->id }})"
                                                                style="border: 2px solid #19345e; color: #19345e; background-color: transparent;">
                                                                <i class="fas fa-magnifying-glass"></i>
                                                            </button>
                                                        </div>

                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="7" class="text-center py-5">
                                                    <i class="fas fa-check-circle fa-4x mb-3"
                                                        style="color: #28a745; opacity: 0.7;"></i>
                                                    <h4 style="color: #28a745;">No failed transactions found</h4>
                                                    <p class="text-muted">Great! All transactions are successful</p>
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="Failed transactions pagination">
                    <ul id="pagination-links" class="pagination pagination-lg">
                        @if ($failedTransactions->hasPages())
                            @if ($failedTransactions->onFirstPage())
                                <li class="page-item disabled">
                                    <a class="page-link" href="#"><i class="fas fa-angle-double-left"></i></a>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $failedTransactions->previousPageUrl() }}"><i
                                            class="fas fa-angle-double-left"></i></a>
                                </li>
                            @endif

                            @foreach ($failedTransactions->getUrlRange(1, $failedTransactions->lastPage()) as $page => $url)
                                <li class="page-item {{ $page == $failedTransactions->currentPage() ? 'active' : '' }}">
                                    <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                </li>
                            @endforeach

                            @if ($failedTransactions->hasMorePages())
                                <li class="page-item">
                                    <a class="page-link" href="{{ $failedTransactions->nextPageUrl() }}"><i
                                            class="fas fa-angle-double-right"></i></a>
                                </li>
                            @else
                                <li class="page-item disabled">
                                    <a class="page-link" href="#"><i class="fas fa-angle-double-right"></i></a>
                                </li>
                            @endif
                        @endif
                    </ul>
                </nav>
            </div>
        </div>
    </section>



@endsection

@push('styles')
    <style>
        .pagination .page-item.disabled {
            cursor: not-allowed;
        }

        .pagination .page-item.disabled .page-link {
            cursor: not-allowed;
            pointer-events: none;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-retry_pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-retry_processing {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-resolved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-abandoned {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border-radius: 0.25rem;
            margin: 0.125rem;
        }
    </style>

    @vite('resources/css/pages/failed-transactions.css')
@endpush

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let debounceTimer;

            function fetchFailedTransactions(url = "/admin/failed-transactions-api", search = "", filters = {}) {
                const paginationLinks = document.getElementById("pagination-links");
                let tbody = document.getElementById("failed-transactions-table-body");

                if (tbody) {
                    tbody.innerHTML = `
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div class="spinner-border" role="status" style="color: #19345E; width: 3rem; height: 3rem;"></div>
                                    <p class="mt-3 text-muted">Loading failed transactions...</p>
                                </td>
                            </tr>
                        `;
                }

                if (paginationLinks) {
                    paginationLinks.innerHTML = "";
                }

                const fullUrl = new URL(url, window.location.origin);
                fullUrl.searchParams.set("search", search);

                // Add filters
                Object.keys(filters).forEach(key => {
                    if (filters[key]) {
                        fullUrl.searchParams.set(key, filters[key]);
                    }
                });

                console.log('Fetching from:', fullUrl.toString());

                fetch(fullUrl)
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Received data:', data);
                        tbody = document.getElementById("failed-transactions-table-body");

                        if (!tbody) {
                            console.error("Table body element not found");
                            return;
                        }

                        tbody.innerHTML = "";

                        if (!data.data || data.data.length === 0) {
                            tbody.innerHTML = `
                                    <tr>
                                        <td colspan="7" class="text-center py-5">
                                            <i class="fas fa-check-circle fa-4x mb-3" style="color: #28a745; opacity: 0.7;"></i>
                                            <h4 style="color: #28a745;">No failed transactions found</h4>
                                            <p class="text-muted">Great! All transactions are successful</p>
                                        </td>
                                    </tr>
                                `;
                            return;
                        }

                        (data.data || []).forEach(transaction => {
                            const row = document.createElement("tr");

                            const statusClass = `status-${transaction.status}`;
                            const statusText = transaction.status.replace('_', ' ').replace(/\b\w/g,
                                l => l.toUpperCase());

                            row.innerHTML = `
                                    <td class="py-3 px-4">
                                        <div class="d-flex align-items-center">
                                            <div class="donor-avatar primary">${(transaction.customer_name || 'U').charAt(0).toUpperCase()}</div>
                                            <div>
                                                <div class="fw-semibold">${transaction.customer_name || 'Unknown'}</div>
                                                <small class="text-muted">${transaction.customer_email || 'No email'}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="fw-semibold" style="color: #F05522;">$${parseFloat(transaction.amount).toLocaleString()}</div>
                                        ${transaction.amount_charged > 0 ? `<small class="text-warning">Charged: $${parseFloat(transaction.amount_charged).toLocaleString()}</small>` : ''}
                                    </td>
                                    <td class="py-3 px-4 text-center">
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <small>${transaction.failure_stage || 'Unknown'}</small>
                                    </td>
                                    <td class="py-3 px-4">
                                        <small title="${transaction.failure_code || 'No message'}">${transaction.failure_code || 'Unknown'}</small>
                                    </td>
                                    <td class="py-3 px-4">
                                        <small>${new Date(transaction.failed_at).toLocaleDateString()}</small>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="d-flex flex-wrap gap-1">


                                             <button class="btn btn-sm action-btn"
                                                                onclick="viewTransaction(${transaction.id})"
                                                                style="border: 2px solid #19345e; color: #19345e; background-color: transparent;">
                                                                <i class="fas fa-magnifying-glass"></i>
                                                            </button>
                                        </div>
                                    </td>
                                `;

                            tbody.appendChild(row);
                        });

                        // Handle pagination
                        if (data.data && data.data.total > data.data.per_page && data.data.last_page > 1) {
                            // Add pagination links similar to donors page
                            let firstPage = document.createElement("li");
                            firstPage.className = `page-item ${data.data.current_page === 1 ? 'disabled' : ''}`;
                            firstPage.innerHTML =
                                `<a class="page-link" href="#"><i class="fas fa-angle-double-left"></i></a>`;
                            firstPage.addEventListener("click", e => {
                                e.preventDefault();
                                if (data.data.current_page !== 1) {
                                    fetchFailedTransactions(data.data.prev_page_url, search, filters);
                                }
                            });
                            paginationLinks.appendChild(firstPage);

                            data.data.links.forEach((link, index) => {
                                if (!link.url || index === 0 || index === data.data.links.length - 1)
                                    return;

                                let pageLink = document.createElement("li");
                                pageLink.className = `page-item ${link.active ? 'active' : ''}`;
                                pageLink.innerHTML =
                                    `<a class="page-link" href="#">${link.label.replace(/<.*?>/g, '')}</a>`;
                                pageLink.addEventListener("click", e => {
                                    e.preventDefault();
                                    fetchFailedTransactions(link.url, search, filters);
                                });
                                paginationLinks.appendChild(pageLink);
                            });

                            let lastPage = document.createElement("li");
                            lastPage.className =
                                `page-item ${data.data.current_page === data.data.last_page ? 'disabled' : ''}`;
                            lastPage.innerHTML =
                                `<a class="page-link" href="#"><i class="fas fa-angle-double-right"></i></a>`;
                            lastPage.addEventListener("click", e => {
                                e.preventDefault();
                                if (data.data.current_page !== data.data.last_page) {
                                    fetchFailedTransactions(data.data.next_page_url, search, filters);
                                }
                            });
                            paginationLinks.appendChild(lastPage);
                        }
                    })
                    .catch(error => {
                        console.error("Error fetching failed transactions:", error);
                        const tbody = document.getElementById("failed-transactions-table-body");
                        if (tbody) {
                            tbody.innerHTML = `
                                    <tr>
                                        <td colspan="7" class="text-center py-5">
                                            <div class="mb-3"><i class="fas fa-exclamation-triangle fa-3x text-warning"></i></div>
                                            <h4 class="text-danger">Something went wrong</h4>
                                            <p class="text-muted">Please try again later or contact support.</p>
                                            <button class="btn btn-outline-primary mt-3" onclick="fetchFailedTransactions()">
                                                <i class="fas fa-sync-alt me-2"></i> Try Again
                                            </button>
                                        </td>
                                    </tr>
                                `;
                        }
                    });
            }

            // Search functionality
            document.getElementById("search").addEventListener("input", function() {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    const search = this.value.trim();
                    const filters = getFilters();
                    fetchFailedTransactions("/admin/failed-transactions-api", search, filters);
                }, 300);
            });

            // Filter functionality
            function getFilters() {
                return {
                    date_from: document.getElementById("date-from").value,
                    date_to: document.getElementById("date-to").value
                };
            }

            document.getElementById("date-from").addEventListener("change", function() {
                const search = document.getElementById("search").value.trim();
                const filters = getFilters();
                fetchFailedTransactions("/admin/failed-transactions-api", search, filters);
            });

            document.getElementById("date-to").addEventListener("change", function() {
                const search = document.getElementById("search").value.trim();
                const filters = getFilters();
                fetchFailedTransactions("/admin/failed-transactions-api", search, filters);
            });

            // Remove animation classes to ensure visibility
            document.querySelectorAll('.animated').forEach(element => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            });

            // Export functionality
            document.getElementById("exportFailedTransactions").addEventListener("click", function() {
                const downloadLink = document.createElement('a');
                downloadLink.href = "{{ route('admin.failed-transactions.export') }}";
                downloadLink.setAttribute('download', 'failed-transactions.csv');
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            });
        });

        // Global functions for actions
        function viewTransaction(id) {
            window.location.href = `/admin/failed-transactions/${id}`;
        }
    </script>
@endpush
