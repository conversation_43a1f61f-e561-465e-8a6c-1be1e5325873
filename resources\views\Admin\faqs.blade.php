@extends('layouts.app')
@section('title', 'FAQ Management')
@section('no-header-footer', 'true')
@section('content')
@include('layouts.admin-nav')
    <div class="faq-admin-wrapper">
        {{-- Hero Section --}}
        <section class="hero-section text-white"
            style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); padding: 60px 0;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-12 text-center">
                        <h1 class="mb-2 fw-bold" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                            FAQ Management
                        </h1>
                        <p class="lead mb-0" style="color: rgba(255,255,255,0.9);">
                            Add, edit, and manage frequently asked questions for your website.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        {{-- FAQ Management Section --}}
        <section class="faq-management-section py-5">
            <div class="container">
                <div class="row mb-4">
                    <div class="col-12 d-flex justify-content-between align-items-center">
                        <h3 style="color: #19345e;">
                            <i class="fas fa-question-circle me-2"></i>Manage FAQs
                        </h3>
                        <button type="button" class="btn rounded-pill px-4 py-2 text-white" data-bs-toggle="modal" data-bs-target="#addFaqModal"
                            style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); border: none; font-weight: 500; transition: all 0.3s ease;"
                            onmouseover="this.style.transform='scale(1.03)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="fas fa-plus me-2"></i>Add New FAQ
                        </button>
                    </div>
                </div>

                <div class="card shadow-sm mb-4" style="border-radius: 15px; overflow: hidden; border: none;">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead style="background: linear-gradient(135deg, rgba(106,17,203,0.1) 0%, rgba(37,117,252,0.1) 100%);">
                                    <tr>
                                        <th class="ps-4" scope="col" width="5%">ID</th>
                                        <th scope="col" width="40%">Question</th>
                                        <th scope="col" width="35%">Answer</th>
                                        <th scope="col" width="10%">Order</th>
                                        <th scope="col" width="10%" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($faqs as $faq)
                                        <tr>
                                            <td class="ps-4">{{ $faq->id }}</td>
                                            <td>{{ Str::limit($faq->question, 70) }}</td>
                                            <td>{{ Str::limit($faq->answer, 70) }}</td>
                                            <td>{{ $faq->order }}</td>
                                            <td>
                                                <div class="d-flex justify-content-center gap-2">
                                                    <button class="btn btn-sm edit-btn" data-bs-toggle="modal" data-bs-target="#editFaqModal"
                                                        data-id="{{ $faq->id }}"
                                                        data-question="{{ $faq->question }}"
                                                        data-answer="{{ $faq->answer }}"
                                                        data-order="{{ $faq->order }}"
                                                        style="color: #2575fc; background-color: rgba(37,117,252,0.1); border: none;"
                                                        title="Edit FAQ">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form action="{{ route('admin.faqs.delete') }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')

                                                        <input type="hidden" name="id" value="{{$faq->id}}">
                                                        <button type="submit" class="btn btn-sm delete-btn"
                                                            style="color: #dc3545; background-color: rgba(220,53,69,0.1); border: none;"
                                                            title="Delete FAQ"
                                                            >
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center py-3">
                                                    <i class="fas fa-question-circle mb-3" style="font-size: 3rem; color: #19345e; opacity: 0.5;"></i>
                                                    <h5 style="color: #19345e;">No FAQs Found</h5>
                                                    <p class="text-muted">Add your first FAQ to get started.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {{-- Pagination --}}
                @if($faqs->hasPages())
                    <div class="d-flex justify-content-center">
                        {{ $faqs->links() }}
                    </div>
                @endif

                {{-- Quick Tips --}}
                <div class="card shadow-sm mt-4" style="border-radius: 15px; overflow: hidden; border: none; background: linear-gradient(145deg, #f0f0f0, #ffffff);">
                    <div class="card-body p-4">
                        <h5 style="color: #19345e;"><i class="fas fa-lightbulb me-2"></i>Tips for Effective FAQs</h5>
                        <ul class="mb-0 ps-3">
                            <li class="mb-2">Keep questions concise and easy to understand.</li>
                            <li class="mb-2">Use clear, jargon-free language in your answers.</li>
                            <li class="mb-2">Organize FAQs by order of importance or topic relevance.</li>
                            <li class="mb-2">Regularly update FAQs based on common user inquiries.</li>
                            <li>Include actionable information in your responses when possible.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    {{-- Add FAQ Modal --}}
    <div class="modal fade" id="addFaqModal" tabindex="-1" aria-labelledby="addFaqModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content shadow-lg border-0" style="border-radius: 24px;">
                <div class="modal-header text-white px-5 py-4"
                    style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); border-top-left-radius: 24px; border-top-right-radius: 24px;">
                    <h4 class="modal-title d-flex align-items-center gap-2" id="addFaqModalLabel">
                        <i class="fas fa-question-circle fa-lg"></i> Create New FAQ
                    </h4>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"
                        style="filter: brightness(90%);"></button>
                </div>

                <form action="{{ route('admin.faqs.store') }}" method="POST">
                    @csrf
                    <div class="modal-body px-5 py-4">
                        <div class="mb-4">
                            <label for="question" class="form-label fw-bold text-primary fs-5">📝 Question</label>
                            <textarea class="form-control fs-5" id="question" name="question" rows="4" required
                                style="border-radius: 16px; border: 1px solid rgba(7, 4, 163, 0.2); padding: 20px; resize: vertical;"
                                placeholder="Enter the full FAQ question here..."></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="answer" class="form-label fw-bold text-primary fs-5">💬 Answer</label>
                            <textarea class="form-control fs-5" id="answer" name="answer" rows="8" required
                                style="border-radius: 16px; border: 1px solid rgba(7,4,163,0.2); padding: 20px; resize: vertical;"
                                placeholder="Provide a detailed answer..."></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="order" class="form-label fw-bold text-primary fs-5">📊 Display Order</label>
                            <input type="number" class="form-control fs-5" id="order" name="order" min="1" value="1"
                                style="border-radius: 16px; border: 1px solid rgba(7,4,163,0.2); padding: 15px;">
                            <small class="form-text text-muted">Lower numbers appear first in the FAQ list.</small>
                        </div>
                    </div>

                    <div class="modal-footer px-5 pb-4">
                        <button type="button" class="btn btn-outline-secondary btn-lg rounded-pill" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-lg rounded-pill text-white px-4"
                            style="background: linear-gradient(135deg, #f05522 0%, #f05522 100%); border: none;">
                            <i class="fas fa-save me-2"></i> Save FAQ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>



    {{-- Edit FAQ Modal --}}
  <div class="modal fade" id="editFaqModal" tabindex="-1" aria-labelledby="editFaqModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content shadow-lg border-0" style="border-radius: 24px;">
            <div class="modal-header text-white px-5 py-4"
                style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); border-top-left-radius: 24px; border-top-right-radius: 24px;">
                <h4 class="modal-title d-flex align-items-center gap-2" id="editFaqModalLabel">
                    <i class="fas fa-edit fa-lg"></i> Edit FAQ
                </h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"
                    style="filter: brightness(90%);"></button>
            </div>

            <form id="editFaqForm" action="{{ route('admin.faqs.update', 0) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" id="editFaqId" name="id">

                <div class="modal-body px-5 py-4">
                    <div class="mb-4">
                        <label for="editQuestion" class="form-label fw-bold text-primary fs-5">📝 Question</label>
                        <textarea class="form-control fs-5" id="editQuestion" name="question" rows="4" required
                            style="border-radius: 16px; border: 1px solid rgba(106,17,203,0.2); padding: 20px; resize: vertical;"
                            placeholder="Update the question..."></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="editAnswer" class="form-label fw-bold text-primary fs-5">💬 Answer</label>
                        <textarea class="form-control fs-5" id="editAnswer" name="answer" rows="8" required
                            style="border-radius: 16px; border: 1px solid rgba(106,17,203,0.2); padding: 20px; resize: vertical;"
                            placeholder="Update the answer..."></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="editOrder" class="form-label fw-bold text-primary fs-5">📊 Display Order</label>
                        <input type="number" class="form-control fs-5" id="editOrder" name="order" min="1"
                            style="border-radius: 16px; border: 1px solid rgba(106,17,203,0.2); padding: 15px;">
                        <small class="form-text text-muted">Lower numbers will appear first in the list.</small>
                    </div>
                </div>

                <div class="modal-footer px-5 pb-4">
                    <button type="button" class="btn btn-outline-secondary btn-lg rounded-pill" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-lg rounded-pill text-white px-4"
                        style="background: linear-gradient(135deg, #19345e 0%, #5070a8 100%); border: none;">
                        <i class="fas fa-save me-2"></i> Update FAQ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content border-0">
        <div class="modal-header">
          <h5 class="modal-title">Confirm Delete</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          Are you sure you want to delete this FAQ?
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
          <button type="button" id="confirmDeleteBtn" class="btn btn-danger">
            <i class="fas fa-spinner fa-spin me-2 d-none"></i> Yes, Delete
          </button>
        </div>
      </div>
    </div>
  </div>


@endsection
@push('styles')
    <style>
        .table th, .table td {
            padding: 0.75rem 1rem;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            border-color: #6a11cb;
        }

        .pagination .page-link {
            color: #6a11cb;
        }

        .pagination .page-link:focus {
            box-shadow: 0 0 0 0.25rem rgba(106, 17, 203, 0.25);
        }

        .btn:focus, .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(106, 17, 203, 0.25);
        }

        .form-control:focus {
            border-color: rgba(106,17,203,0.5);
        }

        .edit-btn:hover, .delete-btn:hover {
            opacity: 0.8;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }
    </style>
@endpush

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Edit modal data population
            const editFaqModal = document.getElementById('editFaqModal');
            if (editFaqModal) {
                editFaqModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const id = button.getAttribute('data-id');
                    const question = button.getAttribute('data-question');
                    const answer = button.getAttribute('data-answer');
                    const order = button.getAttribute('data-order');

                    const form = document.getElementById('editFaqForm');
                    form.action = form.action.replace(/\/\d+$/, '/' + id);

                    document.getElementById('editFaqId').value = id;
                    document.getElementById('editQuestion').value = question;
                    document.getElementById('editAnswer').value = answer;
                    document.getElementById('editOrder').value = order;
                });
            }
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });



            const toggleLoader = (btn, loading) => {
        btn.disabled = loading;
        const icon = btn.querySelector('i');
        if (icon) icon.className = loading ? 'fas fa-spinner fa-spin me-2' : btn.dataset.icon;
    };

    // Create FAQ
    const createForm = document.querySelector('#addFaqModal form');
    if (createForm) {
        const submitBtn = createForm.querySelector('button[type="submit"]');
        submitBtn.dataset.icon = 'fas fa-save me-2';

        createForm.addEventListener('submit', e => {
            e.preventDefault();
            toggleLoader(submitBtn, true);
            fetch(createForm.action, {
                method: 'POST',
                body: new FormData(createForm),
                headers: { 'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                 }
            }).then(res => res.json())
              .then(data => {
                  if (data.success) {
                      createSlider('success', 'FAQ created successfully', { title: 'Success' });
                      setTimeout(() => location.reload(), 1000);
                  } else {
                      throw new Error(data.message || 'Something went wrong');
                  }
              }).catch(err => {
                  createSlider('error', err.message, { title: 'Error' });
              }).finally(() => toggleLoader(submitBtn, false));
        });
    }


    document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            document.getElementById('editFaqId').value = btn.dataset.id;
            document.getElementById('editQuestion').value = btn.dataset.question;
            document.getElementById('editAnswer').value = btn.dataset.answer;
            document.getElementById('editOrder').value = btn.dataset.order;
        });
    });

    // Update FAQ
    const editForm = document.querySelector('#editFaqForm');
    if (editForm) {
        const updateBtn = editForm.querySelector('button[type="submit"]');
        updateBtn.dataset.icon = 'fas fa-save me-2';

        editForm.addEventListener('submit', e => {
            e.preventDefault();
            toggleLoader(updateBtn, true);
            const url = route('admin.faqs.update');
            fetch(url, {
                method: 'POST',
                body: new FormData(editForm),
                headers: { 'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content }
            }).then(res => res.json())
              .then(data => {
                  if (data.success) {
                      createSlider('success', 'FAQ updated successfully', { title: 'Success' });
                      setTimeout(() => location.reload(), 1000);
                  } else {
                      throw new Error(data.message || 'Update failed');
                  }
              }).catch(err => {
                  createSlider('error', err.message, { title: 'Error' });
              }).finally(() => toggleLoader(updateBtn, false));
        });
    }

    // Delete FAQ
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    let targetForm = null;


    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', e => {
            e.preventDefault();
            targetForm = btn.closest('form');
            modal.show();
        });
    });


    confirmBtn.addEventListener('click', () => {
        if (!targetForm) return;

        const btnIcon = confirmBtn.querySelector('i');
        confirmBtn.disabled = true;
        btnIcon.classList.remove('d-none');

        fetch(targetForm.action, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: new URLSearchParams({
                _method: 'DELETE',
                id: targetForm.querySelector('[name="id"]').value
            })
        }).then(res => res.json())
          .then(data => {
              if (data.success) {
                  modal.hide();
                  createSlider('success', 'FAQ deleted successfully', { title: 'Deleted' });
                  setTimeout(() => location.reload(), 1000);
              } else {
                  throw new Error(data.message || 'Delete failed');
              }
          }).catch(err => {
              createSlider('error', err.message, { title: 'Error' });
          }).finally(() => {
              confirmBtn.disabled = false;
              btnIcon.classList.add('d-none');
              targetForm = null;
          });
    });
        });
    </script>
@endpush

