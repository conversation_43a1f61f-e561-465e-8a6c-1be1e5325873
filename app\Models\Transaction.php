<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'payment_intent_id',
        'charge_id',           // Store the Stripe charge ID
        'amount',
        'currency',
        'status',
        'payment_method',
        'payment_method_details', // Store card brand, last4, etc.
        'description',
        'metadata',           // Store any additional metadata from Stripe
        'receipt_url',        // Store Stripe receipt URL
        'receipt_number',     // Store Stripe receipt number
        'is_refunded',
        'refund_amount',
        'refund_id',
        'refunded_at',
        'donor_id',
        'transaction_date',
        'donation_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'is_refunded' => 'boolean',
        'refunded_at' => 'datetime',
        'transaction_date' => 'datetime',
        'metadata' => 'array',
        'payment_method_details' => 'array',
    ];

    /**
     * Get the donor that owns the transaction.
     */
    public function donor()
    {
        return $this->belongsTo(Donor::class);
    }
}
