<?php

namespace App\Http\Controllers;

use App\Services\DonorService;
use App\Models\DonationToken;
use Illuminate\Http\Request;

class DonorController extends Controller
{
    protected $donorService;

    public function __construct(DonorService $donorService)
    {
        $this->donorService = $donorService;
    }

    public function detailView(Request $request)
    {
        $data = $this->donorService->getDetailViewData($request);

        // Generate a donation token
        $donationToken = DonationToken::generateToken(
            $data['amount'],
            $data['user']->slug
        );

        // Redirect to the GET route with the token
        return redirect()->route('donor.payment', ['token' => $donationToken->token]);
    }

    public function paymentView($token)
    {
        // Find the valid token that can be used for payment
        $donationToken = DonationToken::findTokenForPayment($token);

        if (!$donationToken) {
            return redirect()->route('home')->with('error', 'Invalid or expired donation link. Please try again.');
        }

        // Check if token has been successfully used (prevent reuse of successful payments)
        if ($donationToken->isSuccessfullyUsed()) {
            return redirect()->route('home')->with('error', 'This donation link has already been used successfully. Please start a new donation process.');
        }

        // Get the user data using the relationship
        $user = \App\Models\User::where('slug', $donationToken->user_slug)->first();
        $amount = $donationToken->amount;

        // Don't mark token as used yet - only mark when payment succeeds
        // This allows for retry if payment fails

        return response()->view('donor.detail', compact('user', 'amount', 'donationToken'));
    }

    /**
     * Handle payment success - mark token as successfully used
     */
    public function handlePaymentSuccess(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'paymentIntentId' => 'required|string'
        ]);

        $donationToken = DonationToken::where('token', $request->token)->first();

        if (!$donationToken) {
            return response()->json([
                'error' => 'Invalid donation token'
            ], 400);
        }

        // Mark token as successfully used
        $donationToken->markAsSuccess();

        return response()->json([
            'success' => true,
            'message' => 'Payment processed successfully'
        ]);
    }

    /**
     * Handle payment failure - mark token as failed and optionally regenerate
     */
    public function handlePaymentFailure(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'failureReason' => 'nullable|string',
            'regenerateToken' => 'boolean'
        ]);

        $donationToken = DonationToken::where('token', $request->token)->first();

        if (!$donationToken) {
            return response()->json([
                'error' => 'Invalid donation token'
            ], 400);
        }

        // Mark token as failed
        $donationToken->markAsFailed($request->failureReason);

        $response = [
            'success' => true,
            'message' => 'Payment failure recorded',
            'canRetry' => true
        ];

        // If requested, generate a new token for retry
        if ($request->regenerateToken) {
            $newToken = $donationToken->regenerateForRetry();
            $response['newToken'] = $newToken->token;
            $response['newTokenUrl'] = route('donor.payment', ['token' => $newToken->token]);
        }

        return response()->json($response);
    }

    /**
     * Regenerate a new token for retry
     */
    public function regenerateToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        $donationToken = DonationToken::where('token', $request->token)->first();

        if (!$donationToken) {
            return response()->json([
                'error' => 'Invalid donation token'
            ], 400);
        }

        if (!$donationToken->canBeRetried()) {
            return response()->json([
                'error' => 'Token cannot be retried'
            ], 400);
        }

        // Generate new token
        $newToken = $donationToken->regenerateForRetry();

        return response()->json([
            'success' => true,
            'newToken' => $newToken->token,
            'newTokenUrl' => route('donor.payment', ['token' => $newToken->token])
        ]);
    }
}
