/* CSS Variables for Theme */
:root {
    --primary-color: #19345e;
    --secondary-color: #154da3;
    --accent-color: #f05522;
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --text-muted: #999999;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --border-color: #475569;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        var(--primary-color),
        var(--secondary-color)
    );
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.basketball-loader {
    margin-bottom: 2rem;
}

.basketball {
    font-size: 4rem;
    animation: bounce 1.5s infinite;
}

@keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-30px);
    }
    60% {
        transform: translateY(-15px);
    }
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
    text-decoration: none;
}

.logo-icon {
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle,
.search-toggle,
.mobile-menu-toggle {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.theme-toggle:hover,
.search-toggle:hover,
.mobile-menu-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* Search Modal */
.search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 2000;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.search-modal.active {
    opacity: 1;
    visibility: visible;
}

.search-modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 600px;
    max-height: 70vh;
    overflow: hidden;
    transform: translateY(-20px);
    transition: var(--transition);
}

.search-modal.active .search-modal-content {
    transform: translateY(0);
}

.search-box {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    gap: 1rem;
}

.search-box i {
    color: var(--text-muted);
}

#search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1.125rem;
    background: transparent;
    color: var(--text-primary);
}

.search-close {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

/* Container */
.container {
    display: flex;
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    position: fixed;
    left: 0;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    overflow-y: auto;
    transition: var(--transition);
    z-index: 100;
}

.sidebar-content {
    padding: 2rem 1.5rem;
}

.toc-header {
    margin-bottom: 2rem;
}

.toc-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.progress-indicator {
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(
        90deg,
        var(--primary-color),
        var(--accent-color)
    );
    width: 0%;
    transition: width 0.3s ease;
}

/* Table of Contents */
.toc {
    list-style: none;
}

.toc ul {
    list-style: none;
    margin-left: 1rem;
}

.toc li {
    margin-bottom: 0.5rem;
}

.toc a {
    display: block;
    padding: 0.5rem 0.75rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: var(--transition);
    position: relative;
}

.toc a:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.toc a.active {
    color: var(--primary-color);
    background: rgba(25, 52, 94, 0.1);
    font-weight: 500;
}

.toc a.active::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
    min-height: calc(100vh - var(--header-height));
    width: calc(100% - var(--sidebar-width));
    position: relative;
    z-index: 1;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(
        135deg,
        var(--primary-color),
        var(--secondary-color)
    );
    color: white;
    padding: 4rem 2rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin-bottom: 2rem;
    font-size: 0.875rem;
}

.badge-status {
    background: var(--accent-color);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.hero-subtitle {
    display: block;
    font-size: clamp(1.25rem, 3vw, 1.75rem);
    font-weight: 400;
    opacity: 0.9;
    margin-top: 0.5rem;
}

.hero-description {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 0.875rem 2rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: #e04a1f;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Hero Visual Effects */
.hero-visual {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
    animation-delay: var(--delay);
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
}
.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
}
.floating-element:nth-child(3) {
    top: 30%;
    right: 25%;
}
.floating-element:nth-child(4) {
    bottom: 30%;
    left: 20%;
}
.floating-element:nth-child(5) {
    bottom: 20%;
    right: 30%;
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Documentation Content */
.documentation-content {
    padding: 3rem 2rem;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    background: var(--bg-primary);
    min-height: 50vh;
}

.loading-message {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.loading-message p {
    font-size: 1.125rem;
    margin: 0;
}

.doc-section {
    margin-bottom: 4rem;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
    position: relative;
    z-index: 1;
    background: var(--bg-primary);
}

.doc-section.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Animation states for enhanced experience */
.doc-section.will-animate {
    opacity: 0;
    transform: translateY(20px);
}

.doc-section.will-animate.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-actions {
    display: flex;
    gap: 0.5rem;
}

.copy-link {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.copy-link:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

.section-content {
    line-height: 1.7;
    position: relative;
    z-index: 1;
    background: var(--bg-primary);
    padding: 1rem 0;
}

.section-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 2rem 0 1rem 0;
    padding-left: 1rem;
    border-left: 4px solid var(--accent-color);
}

.section-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.5rem 0 0.75rem 0;
}

.section-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.section-content ul,
.section-content ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

.section-content li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.section-content li strong {
    color: var(--text-primary);
}

.section-content code {
    background: var(--bg-tertiary);
    color: var(--accent-color);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-family: "JetBrains Mono", monospace;
    font-size: 0.875rem;
}

.section-content pre {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin: 1.5rem 0;
    overflow-x: auto;
    position: relative;
}

.section-content pre code {
    background: none;
    color: var(--text-primary);
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.section-content blockquote {
    background: var(--bg-secondary);
    border-left: 4px solid var(--primary-color);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.section-content blockquote p {
    margin: 0;
    font-style: italic;
}

.section-content a {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: var(--transition);
}

.section-content a:hover {
    border-bottom-color: var(--primary-color);
}

.section-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.section-content th,
.section-content td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.section-content th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

.section-content td {
    color: var(--text-secondary);
}

.subsection {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

.subsection.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Animation states for enhanced experience */
.subsection.will-animate {
    opacity: 0;
    transform: translateY(20px);
}

.subsection.will-animate.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.subsection h3 {
    margin-top: 0 !important;
    border-left: none !important;
    padding-left: 0 !important;
    color: var(--primary-color) !important;
}

/* Search Results Styling */
.search-results {
    max-height: 400px;
    overflow-y: auto;
}

.search-result {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
}

.search-result:hover {
    background: var(--bg-secondary);
}

.search-result:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.search-result-parent {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.search-result-content {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.search-result-content mark {
    background: var(--accent-color);
    color: white;
    padding: 0.125rem 0.25rem;
    border-radius: 2px;
}

.search-no-results {
    padding: 2rem;
    text-align: center;
    color: var(--text-muted);
}

/* Feature Cards */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.feature-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.feature-card h4 {
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-card p {
    margin: 0;
    color: var(--text-secondary);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 100;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 3rem 2rem 2rem;
    margin-left: var(--sidebar-width);
}

.footer-content {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    max-width: 1000px;
    margin: 2rem auto 0;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .footer {
        margin-left: 0;
    }

    .hero-stats {
        gap: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .mobile-menu-toggle {
        display: flex;
    }
}

@media (min-width: 769px) {
    .mobile-menu-toggle {
        display: none;
    }

    .sidebar {
        transform: translateX(0);
        position: fixed;
        left: 0;
        top: var(--header-height);
        width: var(--sidebar-width);
        height: calc(100vh - var(--header-height));
    }

    .main-content {
        margin-left: var(--sidebar-width);
        width: calc(100% - var(--sidebar-width));
    }

    .footer {
        margin-left: var(--sidebar-width);
    }
}

/* Enhanced Features Styles */

/* Code Copy Button */
.copy-code-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    font-size: 0.875rem;
    transition: var(--transition);
    opacity: 0;
}

.copy-code-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

pre:hover .copy-code-btn {
    opacity: 1;
}

/* Section Anchors */
.section-anchor {
    opacity: 0;
    margin-left: 0.5rem;
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.875em;
    transition: var(--transition);
}

h2:hover .section-anchor,
h3:hover .section-anchor,
h4:hover .section-anchor {
    opacity: 1;
}

.section-anchor:hover {
    color: var(--primary-color);
}

/* Tooltips */
.tooltip {
    position: absolute;
    background: var(--text-primary);
    color: var(--bg-primary);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 10000;
    opacity: 0;
    transform: translateY(4px);
    transition: all 0.2s ease;
    pointer-events: none;
}

.tooltip.visible {
    opacity: 1;
    transform: translateY(0);
}

.tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--text-primary);
}

/* Table of Contents Enhancements */
.toc-toggle {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    font-size: 0.75rem;
}

.toc-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.toc-toggle.rotated {
    transform: rotate(-90deg);
}

.toc ul ul.collapsed {
    display: none;
}

/* Keyboard Shortcuts Indicator */
.keyboard-shortcut {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-family: "JetBrains Mono", monospace;
}

/* Enhanced Animations */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInFromRight 0.6s ease-out;
}

.animate-fade-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Improved Focus Styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Scroll Indicators */
.scroll-indicator {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--bg-tertiary);
    z-index: 999;
}

.scroll-indicator-bar {
    height: 100%;
    background: linear-gradient(
        90deg,
        var(--primary-color),
        var(--accent-color)
    );
    width: 0%;
    transition: width 0.1s ease;
}

/* Enhanced Mobile Styles */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .documentation-content {
        padding: 2rem 1rem;
    }

    .section-header h2 {
        font-size: 1.75rem;
    }

    .search-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .copy-code-btn {
        opacity: 1;
    }

    .toc-toggle {
        opacity: 1;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-element {
        animation: none;
    }

    .basketball {
        animation: none;
    }
}

/* Print Optimizations */
@media print {
    .header,
    .sidebar,
    .back-to-top,
    .search-modal,
    .floating-elements,
    .copy-code-btn,
    .section-anchor,
    .toc-toggle {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .doc-section {
        page-break-inside: avoid;
        margin-bottom: 2rem;
    }

    .section-header {
        page-break-after: avoid;
    }

    pre {
        page-break-inside: avoid;
        white-space: pre-wrap;
        border: 1px solid #ccc;
    }

    a {
        color: inherit !important;
        text-decoration: none !important;
    }

    .hero-section {
        background: none !important;
        color: black !important;
        page-break-after: always;
    }

    .footer {
        margin-left: 0 !important;
    }
}
