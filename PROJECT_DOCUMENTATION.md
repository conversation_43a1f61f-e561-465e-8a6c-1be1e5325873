# 🏀 ASFL Gauntlet - Complete Project Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [The ASFL Gauntlet Story](#the-asfl-gauntlet-story)
3. [User Journey & Experience](#user-journey--experience)
4. [Page-by-Page Guide](#page-by-page-guide)
5. [System Architecture](#system-architecture)
6. [Installation Guide](#installation-guide)
7. [User Roles & Permissions](#user-roles--permissions)
8. [Payment System](#payment-system)
9. [Email System](#email-system)
10. [Database Structure](#database-structure)
11. [API Endpoints](#api-endpoints)
12. [Security Features](#security-features)
13. [Troubleshooting](#troubleshooting)

---

## Project Overview

**ASFL Gauntlet** is more than just a fundraising platform—it's a digital embodiment of hope, community, and the relentless fight against cancer. Built with Laravel 11, this comprehensive web application serves as the technological backbone for the "A Shot For Life Gauntlet," a grueling 24-hour basketball marathon that symbolizes the endurance and determination of cancer patients in their battle against the disease.

Since 2017, this event has brought together hundreds of players who don the exclusive black and white ASFL Gauntlet jerseys, playing continuously for 24 hours with the cancer community metaphorically on their backs. The platform transforms this physical event into a digital fundraising powerhouse, enabling participants to create personalized fundraising profiles, collect donations from supporters worldwide, and track their progress toward ambitious fundraising goals.

### The Mission Behind the Code

Every line of code in this application serves a greater purpose: to facilitate meaningful connections between fundraisers and donors, streamline the donation process, and ultimately raise funds for cancer research. The platform handles everything from user registration and profile management to secure payment processing and real-time progress tracking, all while maintaining the personal touch that makes each fundraising story unique.

### Key Technologies

-   **Backend**: Laravel 11 (PHP 8.2+)
-   **Frontend**: Bootstrap 5, JavaScript ES6
-   **Database**: MySQL
-   **Payment**: Stripe Integration
-   **Email**: Laravel Mail with SMTP
-   **File Storage**: Laravel Storage (Local/S3)
-   **Real-time**: Livewire Components

### Project Goals

-   Facilitate online fundraising for cancer research
-   Provide comprehensive admin management tools
-   Enable secure payment processing
-   Track fundraising progress and statistics
-   Maintain donor privacy and data security

---

## The ASFL Gauntlet Story

### What is the Gauntlet?

The [A Shot For Life Gauntlet](https://www.instagram.com/reel/CzjD5xYOiVN/?utm_source=ig_web_copy_link&igsh=MzRlODBiNWFlZA%3D%3D) is a 24-hour marathon basketball event that serves as a powerful symbol of the struggle that cancer patients endure in their fight against the disease. Since 2017, this extraordinary event has taken place at the Starland Sportsplex, where hundreds of players of all ages and skill levels come together for a common cause.

### The Physical Event

The event features multiple courts running simultaneously:

-   **Intense Floor**: For highly competitive players
-   **Moderate Floor**: For recreational players
-   **Casual Floor**: For beginners and families
-   **Beginner Floor**: For those new to basketball

Each participant wears the exclusive ASFL Gauntlet black and white jersey, symbolically carrying the cancer community on their back as they play through the night and into the next day.

### The Digital Extension

This web platform extends the physical event's reach far beyond the gymnasium walls. It allows:

-   **Global Participation**: Supporters worldwide can contribute to the cause
-   **Personal Stories**: Each fundraiser can share their unique connection to the fight against cancer
-   **Real-time Impact**: Donors can see the immediate effect of their contributions
-   **Community Building**: Creates lasting connections between fundraisers and supporters

---

## User Journey & Experience

### The Supporter's Journey

1. **Discovery**: A supporter learns about a specific fundraiser through social media, word of mouth, or the platform's leaderboard
2. **Connection**: They visit the fundraiser's personalized donation page and read their story
3. **Contribution**: They choose a donation amount and complete the secure payment process
4. **Engagement**: They can leave encouraging messages and track the fundraiser's progress
5. **Community**: They become part of a larger movement fighting cancer

### The Fundraiser's Journey

1. **Registration**: They create an account and complete their profile with photos and personal story
2. **Goal Setting**: They establish their fundraising target and craft their message
3. **Sharing**: They distribute their unique donation page URL to their network
4. **Monitoring**: They track donations, view supporter messages, and update their progress
5. **Gratitude**: They connect with donors and share updates on their fundraising journey

### The Administrator's Journey

1. **Oversight**: They monitor all fundraising activity and platform health
2. **Support**: They assist users with technical issues and account management
3. **Communication**: They manage contact form inquiries and send platform-wide updates
4. **Analysis**: They track overall progress toward annual goals and generate reports
5. **Growth**: They create new user accounts and expand the platform's reach

---

## Page-by-Page Guide

### 🏠 Homepage (`/`) - The Digital Front Door

The homepage serves as the welcoming entry point to the ASFL Gauntlet community. It's designed to immediately communicate the platform's purpose while showcasing the collective impact of all fundraisers.

**Key Features:**

-   **Hero Section**: Features the ASFL Gauntlet logo and mission statement
-   **Live Statistics**: Real-time display of total funds raised, number of participants, and progress toward the annual goal
-   **Progress Bar**: Visual representation of how close the community is to reaching their collective fundraising target
-   **Top Fundraisers Carousel**: Showcases the top 5 performing fundraisers with their photos, names, and current totals
-   **Recent Donors**: Displays the latest 5 donations to create a sense of active community participation
-   **Call-to-Action Buttons**: Prominent "Register" and "Donate" buttons to guide user actions

**Technical Implementation:**

-   Uses `HomePageService` to aggregate data from multiple sources
-   JavaScript-powered progress bar that animates based on real donation totals
-   Responsive design that works seamlessly on mobile and desktop
-   Real-time data updates via AJAX calls to `/home-data` endpoint

### 📊 Leaderboard (`/leaderboard`) - The Competition Hub

The leaderboard transforms fundraising into a friendly competition, motivating participants while celebrating their achievements.

**Key Features:**

-   **Sortable Rankings**: Users can sort by total raised, fundraising goal, or alphabetically
-   **Pagination**: Handles large numbers of fundraisers efficiently (16 per page)
-   **Search Functionality**: Allows visitors to find specific fundraisers by name or city
-   **Profile Links**: Each fundraiser entry links directly to their donation page
-   **Progress Indicators**: Visual bars showing each fundraiser's progress toward their goal
-   **Responsive Grid**: Adapts from 4 columns on desktop to single column on mobile

**Technical Implementation:**

-   AJAX-powered sorting and filtering for smooth user experience
-   Stable sorting algorithm prevents duplicate entries across pages
-   Optimized database queries with proper indexing for fast load times
-   Real-time data synchronization with donation updates

### ℹ️ About Page (`/about`) - The Story Behind the Mission

The about page provides context and emotional connection to the cause, explaining the significance of the Gauntlet event.

**Key Features:**

-   **Hero Image**: Powerful visual representation of the basketball event
-   **Mission Statement**: Clear explanation of what the Gauntlet represents
-   **Event History**: Background on the event's origins since 2017
-   **Participation Details**: Information about different skill levels and court types
-   **Community Impact**: Stories and statistics about the event's effect on cancer research

### 📞 Contact Page (`/contact`) - Community Support Hub

The contact page facilitates communication between visitors and administrators while providing helpful information.

**Key Features:**

-   **Contact Form**: Simple three-field form (name, email, message) for inquiries
-   **FAQ Section**: Dynamically loaded frequently asked questions from the database
-   **Real-time Validation**: Client-side form validation with immediate feedback
-   **Success Confirmation**: Modal popup confirming message submission
-   **Admin Notification**: Automatic email alerts to administrators for new messages

**Technical Implementation:**

-   AJAX form submission prevents page reload
-   Server-side validation with detailed error messages
-   Automatic email notifications using Laravel's mail system
-   FAQ content managed through database for easy updates

### 🎯 Donate Page (`/donate`) - General Donation Portal

A dedicated page for supporters who want to contribute to the organization directly rather than to a specific fundraiser.

**Key Features:**

-   **Flexible Donation Amounts**: Preset amounts plus custom input option
-   **Payment Frequency Options**: One-time, monthly, quarterly, and annual donations
-   **Secure Payment Processing**: Full Stripe integration with multiple payment methods
-   **Donor Information Collection**: Comprehensive form for donor details and preferences
-   **Anonymous Options**: Donors can choose to remain anonymous publicly or completely

### 👤 Individual Donation Pages (`/user/{slug}`) - Personal Fundraising Profiles

Each fundraiser gets their own personalized donation page that tells their unique story and facilitates contributions.

**Key Features:**

-   **Personal Hero Section**: Large profile photo and customized "Support [Name]'s Fundraiser" header
-   **Progress Tracking**: Visual progress bar and statistics showing raised amount vs. goal
-   **Personal Message**: Fundraiser's custom message explaining their connection to the cause
-   **Donation Tiers**: Multiple preset donation amounts with descriptive labels:
    -   $25 - Supporter (Basic Donation)
    -   $50 - Advocate (Standard Support)
    -   $100 - Champion (Strong Support)
    -   $250 - Hero (Major Impact)
    -   $500 - Legend (Significant Contribution)
-   **Custom Amount Option**: Input field for donors who want to give a specific amount
-   **Secure Checkout**: Integrated Stripe payment processing with donation tokens for security
-   **Social Sharing**: Easy sharing options for social media platforms

**Technical Implementation:**

-   Unique slug-based URLs for each fundraiser (e.g., `/user/john-smith`)
-   Donation token system prevents payment replay attacks
-   Real-time progress updates after successful donations
-   Responsive design optimized for mobile sharing

### 🔐 Authentication Pages

**Registration Page (`/register`)**
The registration process is designed to be comprehensive yet user-friendly, collecting all necessary information to create effective fundraising profiles.

**Key Features:**

-   Multi-step registration process for new fundraisers
-   Profile photo upload with automatic resizing and validation
-   Program selection (24-hour, 4-hour, or virtual participation)
-   Fundraising goal setting and personal message creation
-   Legal agreements and liability waivers
-   Unique slug generation for personalized URLs

**Login Page (`/login`)**

-   Secure authentication for existing users
-   Role-based redirection (users to dashboard, admins to admin panel)
-   Password reset functionality
-   Remember me option for convenience
-   Prevention of back button navigation after logout for security

### 👤 User Dashboard (`/user/dashboard`) - Fundraiser Command Center

The user dashboard is where fundraisers manage their campaigns and track their progress. It's designed to be both informative and motivational, providing everything a fundraiser needs to succeed.

**Key Features:**

-   **Hero Section**: Personalized welcome with profile photo and fundraising statistics
-   **Progress Overview**: Large, visual progress bar showing funds raised vs. goal with percentage completion
-   **Quick Stats Cards**:
    -   Total amount raised with trend indicators
    -   Number of donors who have contributed
    -   Average donation amount
    -   Goal completion percentage
-   **Recent Donations Table**: List of recent contributions with donor names (respecting anonymity preferences), amounts, and messages
-   **Profile Management**: Easy access to edit personal information, photos, and fundraising message
-   **Sharing Tools**: Quick links to share donation page on social media platforms
-   **Goal Adjustment**: Ability to update fundraising targets as campaigns progress

**Technical Implementation:**

-   Real-time data updates using Laravel's UserService
-   Responsive design optimized for mobile management
-   Secure file upload for profile photos with validation
-   Integration with social sharing APIs

### 👨‍💼 Admin Dashboard (`/admin/dashboard`) - Platform Control Center

The admin dashboard provides comprehensive oversight of the entire platform, enabling administrators to monitor performance, manage users, and maintain the system effectively.

**Key Features:**

-   **Executive Summary Cards**:
    -   Total funds raised across all fundraisers with trend indicators
    -   Number of active fundraisers
    -   Total number of donors
    -   Progress toward annual fundraising goal with visual progress bar
-   **User Management Section**:
    -   Create new fundraiser accounts with full profile setup
    -   Edit existing user profiles and settings
    -   View user statistics and performance metrics
    -   Export user data to CSV for external analysis
-   **Donation Analytics**:
    -   Real-time donation tracking and monitoring
    -   Donor demographics and geographic distribution
    -   Payment method analysis and success rates
    -   Export donation data for comprehensive reporting
-   **Message Management**:
    -   View and respond to contact form submissions
    -   Mark messages as read/replied for organization
    -   Send bulk emails to users or donors
    -   Track response rates and engagement

**Advanced Admin Features:**

-   **Donor Management Page (`/admin/donation-view`)**:
    -   Comprehensive donor analytics including average donations, median donations, and repeat donor statistics
    -   Top donors leaderboard with contribution history
    -   Donation trends and patterns over time
    -   Anonymous donor statistics and privacy compliance
    -   Geographic distribution of donors by city and state
-   **Email Template System (`/admin/mail-template`)**:
    -   Rich text editor for creating custom email campaigns
    -   Template library for common communications
    -   Bulk email sending to specific user groups (fundraisers, donors, or all users)
    -   Email performance tracking and delivery confirmation

---

## System Architecture

### MVC Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Models      │    │   Controllers   │    │     Views       │
│                 │    │                 │    │                 │
│ • User          │◄──►│ • AdminController│◄──►│ • Admin Dashboard│
│ • Donor         │    │ • UserController │    │ • User Profile  │
│ • Message       │    │ • PaymentController│  │ • Donation Pages│
│ • Transaction   │    │ • MessageController│  │ • Email Templates│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Service Layer

-   **AdminService**: Handles admin operations
-   **DonorService**: Manages donation processing
-   **PaymentService**: Stripe integration
-   **HomePageService**: Public page data
-   **UserService**: User management

### Key Components

-   **Authentication**: Laravel Sanctum
-   **Authorization**: Role-based permissions
-   **Queue System**: Background job processing
-   **Event System**: Real-time notifications
-   **File Management**: Image upload and processing

---

## Installation Guide

### Prerequisites

-   PHP 8.2 or higher
-   Composer
-   Node.js & NPM
-   MySQL 8.0+
-   Web server (Apache/Nginx)

### Step 1: Clone Repository

```bash
git clone <repository-url>
cd ASFL-Gauntlet
```

### Step 2: Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### Step 3: Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### Step 4: Database Setup

```bash
# Configure database in .env file
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=asfl_gauntlet
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Run migrations
php artisan migrate

# Seed database
php artisan db:seed
```

### Step 5: Storage Setup

```bash
# Create storage link
php artisan storage:link

# Set permissions
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

### Step 6: Build Assets

```bash
# Development
npm run dev

# Production
npm run build
```

### Step 7: Configure Services

#### Stripe Configuration

```env
STRIPE_KEY=pk_test_your_publishable_key
STRIPE_SECRET=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

#### Email Configuration

```env
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ASFL Gauntlet"
```

### Step 8: Start Development Server

```bash
php artisan serve
```

---

## Features & Functionality

### 🏠 Public Features

-   **Homepage**: Event information and statistics
-   **Leaderboard**: Top fundraisers ranking
-   **About Page**: Mission and event details
-   **Contact Form**: Visitor inquiries
-   **Donation Pages**: Individual fundraiser profiles

### 👤 User Features

-   **Profile Management**: Personal information and photos
-   **Fundraising Goals**: Set and track targets
-   **Donation Tracking**: View received donations
-   **Custom Messages**: Personalized donor communications
-   **Progress Analytics**: Visual progress indicators

### 👨‍💼 Admin Features

-   **Dashboard**: Comprehensive overview
-   **User Management**: Create, edit, delete users
-   **Donation Monitoring**: Track all transactions
-   **Message System**: Handle contact inquiries
-   **Goal Management**: Set fundraising targets
-   **Export Tools**: CSV data exports
-   **Email Templates**: Custom communications

---

## User Roles & Permissions

### Admin Role

```php
Permissions:
✅ Full system access
✅ User management
✅ Financial data access
✅ System configuration
✅ Email management
✅ Export capabilities
```

### User Role (Fundraisers)

```php
Permissions:
✅ Profile management
✅ View own donations
✅ Update fundraising goals
✅ Access personal dashboard
❌ Admin functions
❌ Other users' data
```

### Guest Users

```php
Permissions:
✅ View public pages
✅ Make donations
✅ Contact form access
❌ User accounts
❌ Admin access
```

---

## Admin Dashboard

### 📊 Statistics Overview

-   **Total Users**: Active fundraisers count
-   **Total Raised**: Cumulative donations
-   **Total Donors**: Unique donor count
-   **Goal Progress**: Visual progress tracking

### 🔧 Management Tools

#### User Management

-   Create new fundraiser accounts
-   Edit existing user profiles
-   Delete inactive accounts
-   Export user data to CSV

#### Donation Management

-   View all transactions
-   Track payment statuses
-   Generate donation reports
-   Export donor information

#### Message System

-   View contact form submissions
-   Reply to inquiries via email
-   Mark messages as read/replied
-   Filter and search messages

#### Goal Management

-   Set annual fundraising targets
-   Track progress against goals
-   Update targets as needed
-   Visual progress indicators

### 📈 Analytics Features

-   Real-time statistics
-   Trend indicators (+12%, +8.2%, etc.)
-   Progress visualization
-   Export capabilities

---

## Payment System - The Heart of Fundraising

The payment system is the critical component that transforms good intentions into actual impact. Built on Stripe's robust infrastructure, it handles millions of dollars in donations while maintaining the highest standards of security and user experience.

### The Donation Journey: From Click to Impact

**Step 1: Donation Initiation**
When a supporter decides to contribute, they're presented with carefully designed donation tiers that make giving both meaningful and accessible. The system offers preset amounts ($25, $50, $100, $250, $500) with descriptive labels that help donors understand the impact of their contribution, plus a custom amount option for those who want to give a specific amount.

**Step 2: Secure Token Generation**
Before any payment processing begins, the system generates a unique donation token using Laravel's `DonationToken` model. This token serves multiple purposes:

-   Prevents payment replay attacks
-   Ensures each donation attempt is unique and traceable
-   Provides a secure way to pass donation data between pages
-   Expires after 30 minutes to prevent stale transactions

**Step 3: Donor Information Collection**
The system collects comprehensive donor information while respecting privacy preferences:

-   Personal details (name, email, address)
-   Payment information (handled securely by Stripe)
-   Donation preferences (anonymous options)
-   Personal message for the fundraiser
-   Marketing communication preferences

**Step 4: Payment Processing**
Using Stripe's advanced payment infrastructure, the system:

-   Validates payment methods in real-time
-   Processes payments with industry-leading security
-   Handles multiple payment types (cards, digital wallets, bank transfers)
-   Manages international transactions and currency conversion
-   Provides instant feedback on payment status

**Step 5: Confirmation and Notification**
Upon successful payment:

-   Donor receives immediate confirmation with receipt
-   Fundraiser is notified of the new contribution
-   Admin team receives transaction summary
-   Database is updated with all transaction details
-   Progress bars and statistics are updated in real-time

### Advanced Payment Features

**Recurring Donations**
The platform supports subscription-based giving with flexible frequencies:

-   Monthly recurring donations for sustained support
-   Quarterly donations for regular contributors
-   Annual donations for major supporters
-   Easy subscription management and cancellation

**Payment Security Architecture**

-   **PCI DSS Compliance**: Stripe handles all sensitive card data
-   **Tokenization**: Card details never touch the application servers
-   **Webhook Verification**: All payment confirmations are cryptographically verified
-   **3D Secure**: Additional authentication for high-value or high-risk transactions

**Payment Method Diversity**

-   **Credit/Debit Cards**: Visa, Mastercard, American Express, Discover

### Transaction Lifecycle Management

**Transaction States and Their Meanings**

```php
// Core Transaction States
'pending'    => Payment initiated but not yet confirmed
'processing' => Payment being processed by Stripe
'succeeded'  => Payment completed successfully
'failed'     => Payment declined or failed
'canceled'   => Payment canceled by user or system
'refunded'   => Money returned to donor (partial or full)
'disputed'   => Chargeback initiated by donor's bank
```

**Error Handling and Recovery**
The system includes comprehensive error handling:

-   **Payment Failures**: Clear error messages with suggested solutions
-   **Network Issues**: Automatic retry mechanisms for temporary failures
-   **Card Declines**: Specific guidance based on decline reason
-   **System Failures**: Graceful degradation and manual processing options

## Email System - Connecting Hearts and Minds

The email system serves as the communication backbone of the ASFL Gauntlet platform, fostering connections between donors, fundraisers, and administrators while keeping everyone informed and engaged throughout the fundraising journey.

### The Communication Ecosystem

**Automated Email Workflows**
The platform orchestrates a sophisticated series of automated communications that trigger based on user actions and system events. Each email is carefully crafted to provide value, maintain engagement, and drive action.

#### 📧 New Message Notifications - Instant Admin Alerts

When visitors use the contact form, the system immediately springs into action:

**Trigger Events:**

-   Contact form submission from any page
-   Urgent inquiries requiring immediate attention
-   Technical support requests

**Notification Process:**

1. **Immediate Alert**: All admin users receive instant email notification
2. **Rich Content**: Email includes sender's full details, complete message, and timestamp
3. **Direct Action**: One-click link to view and reply directly from admin dashboard
4. **Professional Presentation**: Branded email template with sender avatar and organized layout

**Template Features:**

-   Sender information prominently displayed with generated avatar
-   Full message content in easy-to-read format
-   Timestamp and urgency indicators
-   Direct link to admin dashboard for immediate response
-   Mobile-optimized design for on-the-go administrators

#### 💳 Payment Confirmations - Celebrating Every Contribution

Every successful donation triggers a cascade of confirmations that acknowledge the donor's generosity and keep fundraisers motivated:

**For Donors:**

-   **Immediate Receipt**: Professional receipt with transaction details, tax information, and donation summary
-   **Impact Statement**: Personalized message showing how their contribution makes a difference
-   **Fundraiser Connection**: Information about the specific fundraiser they supported
-   **Future Engagement**: Options to follow the fundraiser's progress and share their support

**For Fundraisers:**

-   **New Donation Alert**: Instant notification of new contribution with donor details (respecting anonymity preferences)
-   **Progress Update**: Updated statistics showing progress toward their goal
-   **Donor Message**: Any personal message left by the donor
-   **Sharing Tools**: Easy options to thank the donor and share the milestone

**Template Design:**

-   Professional receipt format with clear transaction details
-   Branded design consistent with platform aesthetics
-   Mobile-responsive layout for easy viewing on any device
-   Clear call-to-action buttons for next steps

#### 🎉 User Registration - Welcome to the Community

New fundraiser registration triggers a comprehensive welcome sequence:

**Welcome Email Components:**

-   **Personal Greeting**: Customized welcome message with fundraiser's name
-   **Getting Started Guide**: Step-by-step instructions for profile completion
-   **Dashboard Tour**: Introduction to key features and tools
-   **Success Tips**: Best practices for effective fundraising
-   **Support Resources**: Contact information and help documentation

**Admin Notifications:**

-   **New User Alert**: Notification to administrators about new registrations
-   **Profile Review**: Summary of new user's information for verification

### Advanced Email Features

#### 📨 Custom Email Campaigns - Admin Communication Hub

The admin email system provides powerful tools for platform-wide communication:

**Rich Text Editor:**

-   **WYSIWYG Interface**: Easy-to-use editor for creating professional emails
-   **Template Library**: Pre-built templates for common communications
-   **Image Upload**: Direct image upload and management within emails
-   **Link Management**: Easy insertion and tracking of links

**Recipient Management:**

-   **Targeted Groups**: Send to specific user segments (fundraisers, donors, admins)
-   **Custom Lists**: Create and manage custom recipient lists
-   **Exclusion Options**: Exclude specific users or groups from campaigns
-   **Delivery Scheduling**: Schedule emails for optimal delivery times

### Email Infrastructure and Reliability

**SMTP Configuration Options:**
The platform supports multiple email service providers for maximum reliability:

```php
// Enterprise Email Providers
'gmail'     => Gmail SMTP (development and small scale)
'sendgrid'  => SendGrid (high-volume transactional emails)
'mailgun'   => Mailgun (developer-friendly API)
'ses'       => Amazon SES (cost-effective at scale)
'postmark'  => Postmark (premium deliverability)
'custom'    => Custom SMTP servers (enterprise solutions)
```

**Delivery Optimization:**

-   **Queue Management**: Background processing prevents delays
-   **Retry Logic**: Automatic retry for failed deliveries
-   **Rate Limiting**: Prevents overwhelming email servers
-   **Bounce Handling**: Automatic management of undeliverable emails
-   **Spam Prevention**: Best practices to ensure inbox delivery

**Template System Architecture:**

-   **Responsive Design**: All emails work perfectly on mobile devices
-   **Brand Consistency**: Consistent use of ASFL Gauntlet colors and logos
-   **Accessibility**: Screen reader compatible and high contrast options
-   **Personalization**: Dynamic content based on recipient data
-   **A/B Testing**: Built-in support for testing different email versions

---

## Database Structure - The Foundation of Data

The database architecture is carefully designed to support the complex relationships between fundraisers, donors, and transactions while maintaining data integrity, performance, and scalability. Built on MySQL, it uses Laravel's Eloquent ORM for elegant data management.

### Core Entity Tables

#### 👤 Users Table - The Fundraiser Profiles

The users table serves as the central hub for all fundraiser information, storing both authentication data and fundraising-specific details.

```sql
-- Core Identity Fields
id                  BIGINT PRIMARY KEY AUTO_INCREMENT
name                VARCHAR(255) NOT NULL INDEX
email               VARCHAR(255) UNIQUE NOT NULL
password            VARCHAR(255) NOT NULL  -- Bcrypt hashed
remember_token      VARCHAR(100) NULLABLE

-- Profile Information
profile_photo       VARCHAR(255) NULLABLE  -- Storage path to profile image
address             VARCHAR(255) NOT NULL
city                VARCHAR(255) NOT NULL INDEX  -- Indexed for geographic queries
state               VARCHAR(255) NOT NULL
phone_number        VARCHAR(20) NULLABLE

-- Fundraising Details
fundraising_goal    DECIMAL(10,2) NOT NULL DEFAULT 0.00
fund_raise_message  TEXT NULLABLE  -- Personal story and motivation
slug                VARCHAR(255) UNIQUE NOT NULL  -- URL-friendly identifier

-- Program Participation
commit_fundraising  BOOLEAN DEFAULT FALSE
use_photo_videos    BOOLEAN DEFAULT FALSE
liability_waiver    BOOLEAN DEFAULT FALSE

-- System Fields
last_login_at       TIMESTAMP NULLABLE
created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

**Key Features:**

-   **Unique Slugs**: Each user gets a URL-friendly identifier for their donation page
-   **Geographic Indexing**: City and name fields are indexed for fast leaderboard queries
-   **Flexible Goals**: Decimal precision allows for precise fundraising targets
-   **Rich Profiles**: Support for photos, personal messages, and contact information

#### 💰 Donors Table - The Contribution Records

The donors table captures detailed information about each donation and donor, supporting both anonymous and public recognition preferences.

```sql
-- Core Donor Identity
id                      BIGINT PRIMARY KEY AUTO_INCREMENT
name                    VARCHAR(255) NOT NULL
email                   VARCHAR(255) NOT NULL
address                 VARCHAR(255) NULLABLE
city                    VARCHAR(255) NULLABLE
state                   VARCHAR(255) NULLABLE

-- Donation Details
amount_donate           DECIMAL(10,2) NOT NULL
message_for_fundraiser  TEXT NULLABLE  -- Personal message to fundraiser
payment_frequency       ENUM('one-time', 'monthly', 'quarterly', 'annually') DEFAULT 'one-time'

-- Privacy Preferences
anonymous_for_public    BOOLEAN DEFAULT FALSE  -- Hide from public leaderboards
anonymous_for_all       BOOLEAN DEFAULT FALSE  -- Complete anonymity

-- Payment Integration
customer_id             VARCHAR(255) NULLABLE  -- Stripe customer ID
subscription_id         VARCHAR(255) NULLABLE  -- For recurring donations
donation_token          VARCHAR(255) NULLABLE  -- Security token reference

-- System Fields
created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

**Privacy Architecture:**

-   **Two-Level Anonymity**: Donors can choose public anonymity or complete anonymity
-   **Flexible Recognition**: Supports both one-time and recurring donation patterns
-   **Message System**: Allows personal communication between donors and fundraisers

#### 💬 Messages Table - Communication Hub

The messages table manages all contact form submissions and administrative communications.

```sql
-- Message Content
id          BIGINT PRIMARY KEY AUTO_INCREMENT
name        VARCHAR(255) NOT NULL
email       VARCHAR(255) NOT NULL
message     TEXT NOT NULL

-- Administrative Tracking
is_read     BOOLEAN DEFAULT FALSE
is_replied  BOOLEAN DEFAULT FALSE

-- System Fields
created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

#### 🔄 Transactions Table - Financial Records

The transactions table provides detailed tracking of all payment activities with comprehensive Stripe integration.

```sql
-- Transaction Identity
id                      BIGINT PRIMARY KEY AUTO_INCREMENT
payment_intent_id       VARCHAR(255) UNIQUE NOT NULL  -- Stripe Payment Intent ID
charge_id               VARCHAR(255) NULLABLE         -- Stripe Charge ID

-- Financial Details
amount                  DECIMAL(10,2) NOT NULL
currency                VARCHAR(3) DEFAULT 'usd'
status                  ENUM('pending', 'processing', 'succeeded', 'failed', 'canceled', 'refunded', 'disputed')

-- Payment Method Information
payment_method          VARCHAR(50) NULLABLE          -- card, bank_transfer, etc.
payment_method_details  JSON NULLABLE                 -- Card brand, last4, etc.

-- Transaction Metadata
description             TEXT NULLABLE
metadata                JSON NULLABLE                 -- Additional Stripe metadata
receipt_url             VARCHAR(500) NULLABLE         -- Stripe receipt URL
receipt_number          VARCHAR(100) NULLABLE         -- Stripe receipt number

-- Refund Information
is_refunded             BOOLEAN DEFAULT FALSE
refund_amount           DECIMAL(10,2) NULLABLE
refund_id               VARCHAR(255) NULLABLE
refunded_at             TIMESTAMP NULLABLE

-- Relationships
donor_id                BIGINT NOT NULL
donation_token          VARCHAR(255) NULLABLE

-- System Fields
transaction_date        TIMESTAMP DEFAULT CURRENT_TIMESTAMP
created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

-- Foreign Key Constraints
FOREIGN KEY (donor_id) REFERENCES donors(id) ON DELETE CASCADE
```

### Relationship Tables and Supporting Entities

#### 🔗 User_Donors Pivot Table - The Connection Bridge

This pivot table creates the many-to-many relationship between users and donors, allowing donors to support multiple fundraisers.

```sql
id          BIGINT PRIMARY KEY AUTO_INCREMENT
user_id     BIGINT NOT NULL
donor_id    BIGINT NOT NULL
amount      DECIMAL(10,2) NOT NULL  -- Amount donated to this specific user
created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

-- Foreign Key Constraints
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
FOREIGN KEY (donor_id) REFERENCES donors(id) ON DELETE CASCADE

-- Composite Index for Performance
INDEX idx_user_donor (user_id, donor_id)
```

### Advanced Database Features

#### 🔐 Security and Donation Tokens

```sql
-- Donation Tokens Table
CREATE TABLE donation_tokens (
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    token       VARCHAR(32) UNIQUE NOT NULL,
    amount      DECIMAL(10,2) NOT NULL,
    user_slug   VARCHAR(255) NOT NULL,
    expires_at  TIMESTAMP NOT NULL,
    used_at     TIMESTAMP NULLABLE,
    status      ENUM('pending', 'used', 'expired', 'failed') DEFAULT 'pending',
    failure_reason TEXT NULLABLE,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 📊 System Configuration

```sql
-- Settings Table for Dynamic Configuration
CREATE TABLE settings (
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    year        YEAR NOT NULL,
    goal_amount DECIMAL(12,2) NOT NULL,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_year (year)
);
```

### Data Relationships and Integrity

**Complex Relationship Mapping:**

```php
// User Model Relationships
User::class
├── belongsToMany(Donor::class) through 'user_donors' pivot
├── hasMany(Transaction::class) through donors
└── hasMany(Program::class) through 'user_programs' pivot

// Donor Model Relationships
Donor::class
├── belongsToMany(User::class) through 'user_donors' pivot
├── hasMany(Transaction::class)
└── hasOne(Subscription::class) for recurring donations

// Transaction Model Relationships
Transaction::class
├── belongsTo(Donor::class)
└── belongsTo(DonationToken::class)
```

**Database Performance Optimizations:**

-   **Strategic Indexing**: Key fields like user names, cities, and email addresses are indexed
-   **Composite Indexes**: Multi-column indexes on frequently queried combinations
-   **Foreign Key Constraints**: Ensure referential integrity across all relationships
-   **Optimized Queries**: Laravel Eloquent relationships prevent N+1 query problems

---

## API Endpoints

### Public Endpoints

```http
GET  /                          # Homepage
GET  /leaderboard              # Fundraiser rankings
GET  /about                    # About page
GET  /contact                  # Contact page
POST /send-message             # Contact form
GET  /user/{slug}              # Donation page
```

### Admin Endpoints

```http
GET  /admin/dashboard          # Admin dashboard
GET  /admin/get-all-users      # User list API
GET  /admin/get-all-donors     # Donor list API
GET  /admin/export-donors      # Export donors CSV
GET  /admin/export-fundraisers # Export users CSV
POST /admin/add-user-account   # Create user
POST /admin/add-admin-account  # Create admin
```

### Payment Endpoints

```http
POST /create-payment-intent    # Initialize payment
POST /store-transaction        # Save transaction
POST /donation-token/success   # Payment success
POST /donation-token/failure   # Payment failure
```

### Message Endpoints

```http
GET  /admin/messages           # Message list
GET  /admin/messages/{id}      # Message details
POST /admin/messages/{id}/reply # Send reply
POST /admin/messages/{id}/read  # Mark as read
```

---

## Security Features

### Authentication & Authorization

-   **Password Hashing**: Bcrypt encryption
-   **Session Management**: Secure session handling
-   **CSRF Protection**: Token-based form protection
-   **Role-based Access**: Admin/User permissions

### Data Protection

-   **Input Validation**: Server-side validation
-   **SQL Injection Prevention**: Eloquent ORM
-   **XSS Protection**: Output escaping
-   **File Upload Security**: Type and size validation

### Payment Security

-   **PCI Compliance**: Stripe integration
-   **Token System**: Secure payment tokens
-   **Webhook Verification**: Stripe signature validation
-   **SSL/TLS**: Encrypted data transmission

### Privacy Features

-   **Anonymous Donations**: Optional donor privacy
-   **Data Encryption**: Sensitive data protection
-   **GDPR Compliance**: Data protection standards
-   **Audit Logging**: Activity tracking

---

## Troubleshooting

### Common Issues

#### Installation Problems

```bash
# Composer issues
composer clear-cache
composer install --no-cache

# Permission errors
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/
```

#### Database Issues

```bash
# Migration errors
php artisan migrate:fresh --seed

# Connection problems
php artisan config:clear
php artisan cache:clear
```

#### Payment Issues

```bash
# Stripe webhook errors
- Verify webhook URL in Stripe dashboard
- Check webhook secret in .env file
- Ensure SSL certificate is valid
```

#### Email Problems

```bash
# SMTP configuration
- Verify SMTP credentials
- Check firewall settings
- Test with mail:test command
```

### Performance Optimization

#### Database Optimization

```php
// Add indexes for frequently queried columns
Schema::table('users', function (Blueprint $table) {
    $table->index(['email', 'slug']);
});

// Use eager loading to prevent N+1 queries
User::with('donors')->get();
```

#### Caching Strategy

```php
// Cache expensive queries
Cache::remember('leaderboard', 3600, function () {
    return User::with('donors')->orderBy('total_raised', 'desc')->get();
});
```

#### Asset Optimization

```bash
# Minify CSS/JS
npm run build

# Optimize images
php artisan storage:optimize
```

### Monitoring & Logging

#### Log Files

```bash
# Application logs
storage/logs/laravel.log

# Web server logs
/var/log/apache2/error.log
/var/log/nginx/error.log
```

#### Performance Monitoring

```php
// Enable query logging
DB::enableQueryLog();

// Monitor slow queries
Log::info('Slow query detected', DB::getQueryLog());
```

---

## Deployment Guide

### Production Checklist

-   [ ] Environment variables configured
-   [ ] Database migrations run
-   [ ] SSL certificate installed
-   [ ] Stripe webhooks configured
-   [ ] Email service configured
-   [ ] File permissions set
-   [ ] Caching enabled
-   [ ] Queue workers running
-   [ ] Backup system configured

### Server Requirements

-   **PHP**: 8.2+ with required extensions
-   **Database**: MySQL 8.0+ or PostgreSQL 13+
-   **Web Server**: Apache 2.4+ or Nginx 1.18+
-   **SSL**: Valid certificate required
-   **Memory**: Minimum 512MB RAM
-   **Storage**: 10GB+ available space

### Maintenance Tasks

```bash
# Daily backups
php artisan backup:run



# Update statistics
php artisan stats:update

# Process queued jobs
php artisan queue:work
```

---
