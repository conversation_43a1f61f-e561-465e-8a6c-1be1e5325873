# 🏀 ASFL Gauntlet - Complete Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Installation Guide](#installation-guide)
4. [Features & Functionality](#features--functionality)
5. [User Roles & Permissions](#user-roles--permissions)
6. [Admin Dashboard](#admin-dashboard)
7. [Payment System](#payment-system)
8. [Email System](#email-system)
9. [Database Structure](#database-structure)
10. [API Endpoints](#api-endpoints)
11. [Security Features](#security-features)
12. [Troubleshooting](#troubleshooting)

---

## Project Overview

**ASFL Gauntlet** is a comprehensive fundraising platform built with Laravel 11, designed to support cancer research through a 24-hour basketball marathon event. The platform enables fundraisers to create profiles, collect donations, and track their progress toward fundraising goals.

### Key Technologies
- **Backend**: Laravel 11 (PHP 8.2+)
- **Frontend**: Bootstrap 5, JavaScript ES6
- **Database**: MySQL
- **Payment**: Stripe Integration
- **Email**: <PERSON><PERSON> Mail with SMTP
- **File Storage**: Laravel Storage (Local/S3)
- **Real-time**: Livewire Components

### Project Goals
- Facilitate online fundraising for cancer research
- Provide comprehensive admin management tools
- Enable secure payment processing
- Track fundraising progress and statistics
- Maintain donor privacy and data security

---

## System Architecture

### MVC Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Models      │    │   Controllers   │    │     Views       │
│                 │    │                 │    │                 │
│ • User          │◄──►│ • AdminController│◄──►│ • Admin Dashboard│
│ • Donor         │    │ • UserController │    │ • User Profile  │
│ • Message       │    │ • PaymentController│  │ • Donation Pages│
│ • Transaction   │    │ • MessageController│  │ • Email Templates│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Service Layer
- **AdminService**: Handles admin operations
- **DonorService**: Manages donation processing
- **PaymentService**: Stripe integration
- **HomePageService**: Public page data
- **UserService**: User management

### Key Components
- **Authentication**: Laravel Sanctum
- **Authorization**: Role-based permissions
- **Queue System**: Background job processing
- **Event System**: Real-time notifications
- **File Management**: Image upload and processing

---

## Installation Guide

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js & NPM
- MySQL 8.0+
- Web server (Apache/Nginx)

### Step 1: Clone Repository
```bash
git clone <repository-url>
cd ASFL-Gauntlet
```

### Step 2: Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### Step 3: Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### Step 4: Database Setup
```bash
# Configure database in .env file
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=asfl_gauntlet
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Run migrations
php artisan migrate

# Seed database
php artisan db:seed
```

### Step 5: Storage Setup
```bash
# Create storage link
php artisan storage:link

# Set permissions
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

### Step 6: Build Assets
```bash
# Development
npm run dev

# Production
npm run build
```

### Step 7: Configure Services

#### Stripe Configuration
```env
STRIPE_KEY=pk_test_your_publishable_key
STRIPE_SECRET=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

#### Email Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ASFL Gauntlet"
```

### Step 8: Start Development Server
```bash
php artisan serve
```

---

## Features & Functionality

### 🏠 Public Features
- **Homepage**: Event information and statistics
- **Leaderboard**: Top fundraisers ranking
- **About Page**: Mission and event details
- **Contact Form**: Visitor inquiries
- **Donation Pages**: Individual fundraiser profiles

### 👤 User Features
- **Profile Management**: Personal information and photos
- **Fundraising Goals**: Set and track targets
- **Donation Tracking**: View received donations
- **Custom Messages**: Personalized donor communications
- **Progress Analytics**: Visual progress indicators

### 👨‍💼 Admin Features
- **Dashboard**: Comprehensive overview
- **User Management**: Create, edit, delete users
- **Donation Monitoring**: Track all transactions
- **Message System**: Handle contact inquiries
- **Goal Management**: Set fundraising targets
- **Export Tools**: CSV data exports
- **Email Templates**: Custom communications

---

## User Roles & Permissions

### Admin Role
```php
Permissions:
✅ Full system access
✅ User management
✅ Financial data access
✅ System configuration
✅ Email management
✅ Export capabilities
```

### User Role (Fundraisers)
```php
Permissions:
✅ Profile management
✅ View own donations
✅ Update fundraising goals
✅ Access personal dashboard
❌ Admin functions
❌ Other users' data
```

### Guest Users
```php
Permissions:
✅ View public pages
✅ Make donations
✅ Contact form access
❌ User accounts
❌ Admin access
```

---

## Admin Dashboard

### 📊 Statistics Overview
- **Total Users**: Active fundraisers count
- **Total Raised**: Cumulative donations
- **Total Donors**: Unique donor count
- **Goal Progress**: Visual progress tracking

### 🔧 Management Tools

#### User Management
- Create new fundraiser accounts
- Edit existing user profiles
- Delete inactive accounts
- Export user data to CSV

#### Donation Management
- View all transactions
- Track payment statuses
- Generate donation reports
- Export donor information

#### Message System
- View contact form submissions
- Reply to inquiries via email
- Mark messages as read/replied
- Filter and search messages

#### Goal Management
- Set annual fundraising targets
- Track progress against goals
- Update targets as needed
- Visual progress indicators

### 📈 Analytics Features
- Real-time statistics
- Trend indicators (+12%, +8.2%, etc.)
- Progress visualization
- Export capabilities

---

## Payment System

### Stripe Integration
The platform uses Stripe for secure payment processing with the following features:

#### Payment Flow
1. **Donation Initiation**: User selects amount and fundraiser
2. **Token Generation**: Secure donation token created
3. **Payment Processing**: Stripe handles card processing
4. **Confirmation**: Success/failure handling
5. **Notification**: Email confirmations sent

#### Security Features
- **PCI Compliance**: Stripe handles sensitive data
- **Token System**: Prevents payment replay attacks
- **Webhook Verification**: Secure payment confirmations
- **Fraud Protection**: Built-in Stripe security

#### Payment Methods
- Credit/Debit Cards
- Digital Wallets (Apple Pay, Google Pay)
- Bank Transfers (ACH)
- International Cards

### Transaction Management
```php
// Transaction States
- pending: Payment initiated
- completed: Successfully processed
- failed: Payment declined
- refunded: Money returned
- disputed: Chargeback initiated
```

---

## Email System

### Automated Emails

#### New Message Notifications
- **Trigger**: Contact form submission
- **Recipients**: All admin users
- **Content**: Sender details, message content, reply link
- **Template**: Custom HTML design

#### Payment Confirmations
- **Trigger**: Successful donation
- **Recipients**: Donor and fundraiser
- **Content**: Transaction details, receipt
- **Template**: Professional receipt format

#### User Registration
- **Trigger**: New account creation
- **Recipients**: New user and admins
- **Content**: Welcome message, login details
- **Template**: Branded welcome email

### Email Templates
All emails use custom HTML templates with:
- Responsive design
- Brand colors and logos
- Professional styling
- Clear call-to-action buttons

### SMTP Configuration
```php
// Supported providers
- Gmail SMTP
- SendGrid
- Mailgun
- Amazon SES
- Custom SMTP servers
```

---

## Database Structure

### Core Tables

#### Users Table
```sql
- id (Primary Key)
- name (VARCHAR)
- email (UNIQUE)
- password (HASHED)
- profile_photo (VARCHAR)
- city (VARCHAR)
- state (VARCHAR)
- fundraising_goal (DECIMAL)
- fund_raise_message (TEXT)
- slug (UNIQUE)
- created_at, updated_at
```

#### Donors Table
```sql
- id (Primary Key)
- name (VARCHAR)
- email (VARCHAR)
- amount_donate (DECIMAL)
- message_for_fundraiser (TEXT)
- anonymous_for_public (BOOLEAN)
- anonymous_for_all (BOOLEAN)
- stripe_payment_intent_id (VARCHAR)
- created_at, updated_at
```

#### Messages Table
```sql
- id (Primary Key)
- name (VARCHAR)
- email (VARCHAR)
- message (TEXT)
- is_read (BOOLEAN)
- is_replied (BOOLEAN)
- created_at, updated_at
```

#### Transactions Table
```sql
- id (Primary Key)
- donor_id (Foreign Key)
- amount (DECIMAL)
- stripe_payment_intent_id (VARCHAR)
- status (ENUM)
- created_at, updated_at
```

### Relationships
```php
User hasMany Donors (through pivot)
Donor belongsToMany Users
Message belongsTo User (admin replies)
Transaction belongsTo Donor
```

---

## API Endpoints

### Public Endpoints
```http
GET  /                          # Homepage
GET  /leaderboard              # Fundraiser rankings
GET  /about                    # About page
GET  /contact                  # Contact page
POST /send-message             # Contact form
GET  /user/{slug}              # Donation page
```

### Admin Endpoints
```http
GET  /admin/dashboard          # Admin dashboard
GET  /admin/get-all-users      # User list API
GET  /admin/get-all-donors     # Donor list API
GET  /admin/export-donors      # Export donors CSV
GET  /admin/export-fundraisers # Export users CSV
POST /admin/add-user-account   # Create user
POST /admin/add-admin-account  # Create admin
```

### Payment Endpoints
```http
POST /create-payment-intent    # Initialize payment
POST /store-transaction        # Save transaction
POST /donation-token/success   # Payment success
POST /donation-token/failure   # Payment failure
```

### Message Endpoints
```http
GET  /admin/messages           # Message list
GET  /admin/messages/{id}      # Message details
POST /admin/messages/{id}/reply # Send reply
POST /admin/messages/{id}/read  # Mark as read
```

---

## Security Features

### Authentication & Authorization
- **Password Hashing**: Bcrypt encryption
- **Session Management**: Secure session handling
- **CSRF Protection**: Token-based form protection
- **Role-based Access**: Admin/User permissions

### Data Protection
- **Input Validation**: Server-side validation
- **SQL Injection Prevention**: Eloquent ORM
- **XSS Protection**: Output escaping
- **File Upload Security**: Type and size validation

### Payment Security
- **PCI Compliance**: Stripe integration
- **Token System**: Secure payment tokens
- **Webhook Verification**: Stripe signature validation
- **SSL/TLS**: Encrypted data transmission

### Privacy Features
- **Anonymous Donations**: Optional donor privacy
- **Data Encryption**: Sensitive data protection
- **GDPR Compliance**: Data protection standards
- **Audit Logging**: Activity tracking

---

## Troubleshooting

### Common Issues

#### Installation Problems
```bash
# Composer issues
composer clear-cache
composer install --no-cache

# Permission errors
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/
```

#### Database Issues
```bash
# Migration errors
php artisan migrate:fresh --seed

# Connection problems
php artisan config:clear
php artisan cache:clear
```

#### Payment Issues
```bash
# Stripe webhook errors
- Verify webhook URL in Stripe dashboard
- Check webhook secret in .env file
- Ensure SSL certificate is valid
```

#### Email Problems
```bash
# SMTP configuration
- Verify SMTP credentials
- Check firewall settings
- Test with mail:test command
```

### Performance Optimization

#### Database Optimization
```php
// Add indexes for frequently queried columns
Schema::table('users', function (Blueprint $table) {
    $table->index(['email', 'slug']);
});

// Use eager loading to prevent N+1 queries
User::with('donors')->get();
```

#### Caching Strategy
```php
// Cache expensive queries
Cache::remember('leaderboard', 3600, function () {
    return User::with('donors')->orderBy('total_raised', 'desc')->get();
});
```

#### Asset Optimization
```bash
# Minify CSS/JS
npm run build

# Optimize images
php artisan storage:optimize
```

### Monitoring & Logging

#### Log Files
```bash
# Application logs
storage/logs/laravel.log

# Web server logs
/var/log/apache2/error.log
/var/log/nginx/error.log
```

#### Performance Monitoring
```php
// Enable query logging
DB::enableQueryLog();

// Monitor slow queries
Log::info('Slow query detected', DB::getQueryLog());
```

---

## Deployment Guide

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] SSL certificate installed
- [ ] Stripe webhooks configured
- [ ] Email service configured
- [ ] File permissions set
- [ ] Caching enabled
- [ ] Queue workers running
- [ ] Backup system configured

### Server Requirements
- **PHP**: 8.2+ with required extensions
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **SSL**: Valid certificate required
- **Memory**: Minimum 512MB RAM
- **Storage**: 10GB+ available space

### Maintenance Tasks
```bash
# Daily backups
php artisan backup:run

# Clear expired tokens
php artisan tokens:cleanup

# Update statistics
php artisan stats:update

# Process queued jobs
php artisan queue:work
```

---

## Support & Contact

### Development Team
- **Project Lead**: [Your Name]
- **Backend Developer**: Laravel Specialist
- **Frontend Developer**: UI/UX Expert
- **DevOps Engineer**: Infrastructure Manager

### Documentation Updates
This documentation is maintained alongside the codebase. For updates or corrections, please:
1. Create an issue in the project repository
2. Submit a pull request with changes
3. Contact the development team directly

### Version History
- **v1.0.0**: Initial release with core functionality
- **v1.1.0**: Added export features and enhanced UI
- **v1.2.0**: Improved email system and notifications
- **v1.3.0**: Enhanced security and performance optimizations

---

*Last Updated: December 2024*
*Documentation Version: 1.3.0*