@extends('layouts.app')

@section('title', 'Message Details')

@section('no-header-footer', 'true')

@section('content')
    @include('layouts.admin-nav')

    <section class="py-5" style="background: #f8fafc; min-height: 100vh;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Header -->
                    <div class="d-flex align-items-center mb-4">
                        <a href="{{ route('admin.messages.index') }}" class="btn btn-outline-primary rounded-pill me-3">
                            <i class="fas fa-arrow-left me-2"></i>Back to Messages
                        </a>
                        <div>
                            <h3 class="fw-bold text-dark mb-1">Message Details</h3>
                            <p class="text-muted mb-0">View and reply to visitor message</p>
                        </div>
                    </div>

                    <!-- Message Card -->
                    <div class="bg-white rounded-4 shadow-sm p-4 mb-4">
                        <div class="d-flex justify-content-between align-items-start mb-4">
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="fw-bold text-dark mb-1">{{ $message->name }}</h5>
                                    <p class="text-muted mb-0">{{ $message->email }}</p>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge {{ $message->is_read ? 'bg-success' : 'bg-warning' }} bg-opacity-10 {{ $message->is_read ? 'text-success' : 'text-warning' }} px-3 py-2 rounded-pill mb-2">
                                    {{ $message->is_read ? 'Read' : 'Unread' }}
                                </span>
                                <br>
                                <small class="text-muted">{{ $message->created_at->format('M d, Y - h:i A') }}</small>
                            </div>
                        </div>

                        <div class="message-content bg-light rounded-3 p-4">
                            <h6 class="fw-semibold text-dark mb-3">Message:</h6>
                            <p class="text-dark mb-0" style="line-height: 1.6;">{{ $message->message }}</p>
                        </div>
                    </div>

                    <!-- Reply Section -->
                    <div class="bg-white rounded-4 shadow-sm p-4">
                        <h5 class="fw-bold text-dark mb-4">
                            <i class="fas fa-reply me-2 text-primary"></i>Reply to Message
                        </h5>

                        @if($message->is_replied)
                            <div class="alert alert-success rounded-3">
                                <i class="fas fa-check-circle me-2"></i>
                                You have already replied to this message.
                            </div>
                        @endif

                        <form id="replyForm" method="POST" action="{{ route('admin.messages.reply', $message->id) }}">
                            @csrf
                            <div class="mb-3">
                                <label for="email" class="form-label fw-semibold">To:</label>
                                <input type="email" class="form-control rounded-pill py-3" id="email" name="email" 
                                       value="{{ $message->email }}" readonly style="background: #f8f9fa;">
                            </div>

                            <div class="mb-4">
                                <label for="reply" class="form-label fw-semibold">Your Reply:</label>
                                <textarea class="form-control rounded-3" id="reply" name="reply" rows="6" 
                                          placeholder="Type your reply here..." required style="resize: vertical;"></textarea>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" id="sendReplyBtn" class="btn btn-primary rounded-pill px-4 py-2">
                                    <span class="reply-text">
                                        <i class="fas fa-paper-plane me-2"></i>Send Reply
                                    </span>
                                    <span class="reply-loader d-none">
                                        <i class="fas fa-spinner fa-spin me-2"></i>Sending...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
document.getElementById('replyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const btn = document.getElementById('sendReplyBtn');
    const replyText = btn.querySelector('.reply-text');
    const replyLoader = btn.querySelector('.reply-loader');
    
    // Show loader
    replyText.classList.add('d-none');
    replyLoader.classList.remove('d-none');
    btn.disabled = true;
    
    // Submit form
    fetch(form.action, {
        method: 'POST',
        body: new FormData(form),
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success rounded-3 mt-3';
            alertDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>Reply sent successfully!';
            form.appendChild(alertDiv);
            
            // Clear form
            document.getElementById('reply').value = '';
            
            // Update status
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            throw new Error('Failed to send reply');
        }
    })
    .catch(error => {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger rounded-3 mt-3';
        alertDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>Failed to send reply. Please try again.';
        form.appendChild(alertDiv);
    })
    .finally(() => {
        // Hide loader
        replyText.classList.remove('d-none');
        replyLoader.classList.add('d-none');
        btn.disabled = false;
    });
});
</script>
@endpush