<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('donation_tokens', function (Blueprint $table) {
            $table->enum('status', ['pending', 'success', 'failed'])
                ->default('pending')
                ->after('token');
            $table->text('failure_reason')
                ->nullable()
                ->after('status');
        });
    }

    public function down()
    {
        Schema::table('donation_tokens', function (Blueprint $table) {
            $table->dropColumn(['status', 'failure_reason']);
        });
    }
};
