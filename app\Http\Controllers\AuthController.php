<?php

namespace App\Http\Controllers;

use App\Events\UserRegistered;
use App\Models\Program;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{

    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }




    public function register(): View
    {
        return view('Auth.register');
    }

    public function store(Request $request): JsonResponse
    {

        $user = $this->userService->storeUser($request);

        Auth::login($user);

        Event::dispatch(new UserRegistered($user));
        return response()->json([
            'success' => true,
            'message' => 'User Account created successfully!',
            'data' => $user,
            // 'redirectUrl' => route('program-selection', ['slug' => $user->slug]),
            'redirectUrl' => route('user.dashboard'),
        ], 201);
    }


    public function programSelectionPage(Request $request, $slug): View|RedirectResponse|Response
    {
        $user = User::where('slug', $slug)->first();

        if (!$user) {
            return response()->view('Errors.errors500');
        }

        return response()->view('Auth.program-selection', compact('user'));
    }

    public function programSelection(Request $request, $slug): JsonResponse
    {
        $programs = $this->userService->getAllPrograms();
        $user = User::where('slug', $slug)->first();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found.',
            ], 404);
        }

        // Load user's selected programs
        $userPrograms = $user->programs()->pluck('name')->toArray();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'slug' => $user->slug,
                ],
                'programs' => $programs,
                'userPrograms' => $userPrograms,
            ],
        ], 200);
    }


    public function selectedProgram(Request $request)
    {


        $request->validate([
            'selected_program' => 'required|string',
            'user_slug' => 'required|string',
        ]);

        $program = Program::where('name', $request->selected_program)->first();

        if (!$program) {
            return response()->json([
                'success' => false,
                'message' => 'Program not found.',
            ], 404);
        }

        $user = User::where('slug', $request->user_slug)->first();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found.',
            ], 404);
        }

        // Check if user already has this program
        $existingProgram = $user->programs()->where('program_id', $program->id)->first();

        if ($existingProgram) {
            // User already has this program, just log them in and redirect
            Auth::login($user);

            return response()->json([
                'success' => true,
                'message' => 'Program selection confirmed.',
                'redirectUrl' => route('user.dashboard'),
            ], 200);
        }

        // Remove any existing programs and attach the new one
        $user->programs()->detach();
        $user->programs()->attach($program->id);

        Auth::login($user);

        return response()->json([
            'success' => true,
            'message' => 'Program selected successfully.',
            'redirectUrl' => route('user.dashboard'),
        ], 200);
    }





    public function login(): View
    {
        return view('Auth.login')->withHeaders([
            'Cache-Control' => 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma'        => 'no-cache',
            'Expires'       => '0',
        ]);
    }



    public function loginUser(Request $request)
    {
        try {
            return $this->userService->authenticateUser($request);
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['error' => 'Something went wrong. Please try again.']);
        }
    }


    public function logout(Request $request): RedirectResponse
    {
        try {
            Auth::logout();

            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('home')->with('success', 'You have been logged out successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Something went wrong. Please try again.');
        }
    }


    public function forgotPasswordPage(Request $request): View
    {
        return view('Auth.forgotPassword');
    }


    public function forgotPassword(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email'
        ]);
        $status = Password::sendResetLink((
            $request->only('email')
        ));

        if ($status === Password::RESET_LINK_SENT) {
            return redirect()->route('forgotPasswordPage')->with([
                'success' => 'Reset link has been sent. Please check your mail.',
                'email' => $request->email
            ]);
        }
        return back()->withErrors(['email' => trans($status)]);
    }






    public function resetPassword(Request $request): RedirectResponse
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('success', 'Password updated Successfully')
            : back()->withErrors(['email' => [__($status)]]);
    }
}
