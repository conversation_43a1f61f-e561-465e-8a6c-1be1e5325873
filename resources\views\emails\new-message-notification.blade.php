<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Message Received</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
        }
        .message-card {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .sender-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }
        .avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            margin-right: 15px;
        }
        .sender-details h3 {
            margin: 0;
            color: #2d3748;
            font-size: 18px;
        }
        .sender-details p {
            margin: 5px 0 0 0;
            color: #718096;
            font-size: 14px;
        }
        .message-content {
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            margin: 15px 0;
        }
        .message-content h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 16px;
        }
        .message-text {
            color: #4a5568;
            line-height: 1.6;
            margin: 0;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .action-button:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        .footer {
            background-color: #f7fafc;
            padding: 20px;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
        .timestamp {
            color: #a0aec0;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 New Message Received</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">You have a new message from a website visitor</p>
        </div>

        <div class="content">
            <div class="message-card">
                <div class="sender-info">
                    <div class="avatar">
                        {{ strtoupper(substr($contactMessage->name, 0, 1)) }}
                    </div>
                    <div class="sender-details">
                        <h3>{{ $contactMessage->name }}</h3>
                        <p>{{ $contactMessage->email }}</p>
                        <div class="timestamp">
                            Received on {{ $contactMessage->created_at->format('F d, Y at h:i A') }}
                        </div>
                    </div>
                </div>

                <div class="message-content">
                    <h4>Message:</h4>
                    <p class="message-text">{{ $contactMessage->message }}</p>
                </div>
            </div>

            <div style="text-align: center;">
                <a href="{{ route('admin.messages.show', $contactMessage->id) }}" class="action-button">
                    View & Reply to Message
                </a>
            </div>

            <p style="color: #718096; font-size: 14px; margin-top: 30px;">
                You can view this message and send a reply directly from your admin dashboard.
                The sender will receive your reply via email.
            </p>
        </div>

        <div class="footer">
            <p>This is an automated notification from your website's contact form.</p>
        </div>
    </div>
</body>
</html>
